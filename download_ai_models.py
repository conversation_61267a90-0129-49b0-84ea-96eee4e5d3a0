"""
Download AI models for the complete matting and inpainting pipeline
"""

import os
import requests
import torch
from pathlib import Path
from tqdm import tqdm


def download_file(url: str, filepath: str, description: str = "Downloading") -> bool:
    """Download file with progress bar"""
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        with open(filepath, 'wb') as file, tqdm(
            desc=description,
            total=total_size,
            unit='B',
            unit_scale=True,
            unit_divisor=1024,
        ) as pbar:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    file.write(chunk)
                    pbar.update(len(chunk))
        
        print(f"✅ Downloaded: {filepath}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to download {url}: {e}")
        return False


def check_gpu_availability():
    """Check GPU availability and CUDA version"""
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        gpu_name = torch.cuda.get_device_name(0)
        cuda_version = torch.version.cuda
        print(f"🚀 GPU Available: {gpu_name}")
        print(f"🔢 GPU Count: {gpu_count}")
        print(f"⚡ CUDA Version: {cuda_version}")
        return True
    else:
        print("💻 No GPU available, will use CPU")
        return False


def download_modnet_model():
    """Download MODNet pretrained model"""
    model_urls = {
        "modnet_photographic_portrait_matting.ckpt": "https://github.com/clibdev/MODNet/releases/latest/download/modnet-photographic.pt",
        "modnet_webcam_portrait_matting.ckpt": "https://github.com/clibdev/MODNet/releases/latest/download/modnet-webcam.pt"
    }
    
    checkpoints_dir = "checkpoints"
    os.makedirs(checkpoints_dir, exist_ok=True)
    
    success_count = 0
    for filename, url in model_urls.items():
        filepath = os.path.join(checkpoints_dir, filename)
        
        if os.path.exists(filepath):
            file_size = os.path.getsize(filepath)
            if file_size > 10_000_000:  # > 10MB, assume complete
                print(f"✅ MODNet model already exists: {filepath} ({file_size / 1024 / 1024:.1f}MB)")
                success_count += 1
                continue
        
        print(f"📥 Downloading MODNet model: {filename}")
        success = download_file(url, filepath, f"Downloading {filename}")
        
        if success:
            print(f"✅ MODNet model downloaded successfully")
            success_count += 1
        else:
            print(f"❌ Failed to download MODNet model")
    
    return success_count > 0


def download_lama_model():
    """Download LaMa inpainting model - simplified approach"""
    print("📝 LaMa model download: Using simplified implementation")
    print("💡 For full LaMa model, install 'simple-lama-inpainting' package:")
    print("   pip install simple-lama-inpainting")
    
    # Create models directory for future use
    models_dir = "models/lama"
    os.makedirs(models_dir, exist_ok=True)
    
    # Create a placeholder file
    placeholder_file = os.path.join(models_dir, "README.txt")
    with open(placeholder_file, 'w') as f:
        f.write("LaMa model directory\n")
        f.write("Install 'simple-lama-inpainting' for full LaMa functionality\n")
        f.write("Current implementation uses enhanced OpenCV inpainting\n")
    
    return True


def verify_sam_models():
    """Verify SAM2 models are available"""
    sam_models = [
        "sam2_tiny.pt",
        "sam2_small.pt", 
        "sam2_base_plus.pt",
        "sam2_large.pt"
    ]
    
    checkpoints_dir = "checkpoints"
    
    print("🔍 Verifying SAM2 models...")
    for model_name in sam_models:
        filepath = os.path.join(checkpoints_dir, model_name)
        if os.path.exists(filepath):
            file_size = os.path.getsize(filepath)
            print(f"✅ {model_name}: {file_size / 1024 / 1024:.1f}MB")
        else:
            print(f"❌ Missing: {model_name}")
    
    return True


def setup_gpu_environment():
    """Setup GPU environment and check compatibility"""
    has_gpu = check_gpu_availability()
    
    if has_gpu:
        try:
            # Test GPU functionality
            device = torch.device('cuda')
            test_tensor = torch.randn(100, 100).to(device)
            result = torch.mm(test_tensor, test_tensor.t())
            print(f"✅ GPU test passed: {result.shape}")
            
            # Check memory
            total_memory = torch.cuda.get_device_properties(0).total_memory
            print(f"💾 GPU Memory: {total_memory / 1024**3:.1f}GB")
            
            return True, device
            
        except Exception as e:
            print(f"⚠️ GPU test failed: {e}")
            print("🔄 Will fallback to CPU")
            return False, torch.device('cpu')
    else:
        return False, torch.device('cpu')


def update_requirements():
    """Update requirements.txt with additional dependencies"""
    additional_deps = [
        "timm>=0.6.0",  # For model architectures
        "albumentations>=1.3.0",  # For image augmentations
        "kornia>=0.6.0",  # For computer vision operations
        "einops>=0.6.0",  # For tensor operations
        "ftfy>=6.1.0",  # For text processing in some models
        "huggingface-hub>=0.16.0",  # For downloading models
        "requests>=2.25.0",  # For downloading files
        "tqdm>=4.60.0",  # For progress bars
    ]
    
    requirements_file = "requirements.txt"
    
    # Read existing requirements
    if os.path.exists(requirements_file):
        with open(requirements_file, 'r', encoding='utf-8') as f:
            existing_deps = f.read().strip().split('\n')
    else:
        existing_deps = []
    
    # Add new dependencies if not present
    updated = False
    for dep in additional_deps:
        dep_name = dep.split('>=')[0].split('==')[0]
        if not any(dep_name in existing_dep for existing_dep in existing_deps):
            existing_deps.append(dep)
            updated = True
            print(f"➕ Added dependency: {dep}")
    
    if updated:
        with open(requirements_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(existing_deps))
        print(f"✅ Updated {requirements_file}")
    else:
        print(f"✅ All dependencies already present in {requirements_file}")


def install_dependencies():
    """Install new dependencies"""
    try:
        import subprocess
        import sys
        
        print("📦 Installing new dependencies...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "requests", "tqdm", "timm", "albumentations", 
            "kornia", "einops", "ftfy", "huggingface-hub"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Dependencies installed successfully")
            return True
        else:
            print(f"⚠️ Some dependencies may have failed to install: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"⚠️ Failed to install dependencies: {e}")
        print("💡 Please manually install: pip install requests tqdm timm albumentations kornia einops ftfy huggingface-hub")
        return False


def main():
    """Main function to download all models"""
    print("=" * 60)
    print("🤖 AI Models Download & Setup Script")
    print("=" * 60)
    
    # Install dependencies first
    print("\n📦 Installing dependencies...")
    install_dependencies()
    
    # Check GPU
    has_gpu, device = setup_gpu_environment()
    
    # Update requirements
    print("\n📦 Updating requirements...")
    update_requirements()
    
    # Verify SAM models
    print("\n🔍 Checking SAM2 models...")
    verify_sam_models()
    
    # Download MODNet
    print("\n🎨 Downloading MODNet models...")
    modnet_success = download_modnet_model()
    
    # Download LaMa (simplified)
    print("\n🖼️ Setting up LaMa models...")
    lama_success = download_lama_model()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 DOWNLOAD SUMMARY")
    print("=" * 60)
    print(f"🖥️  GPU Available: {'✅ Yes' if has_gpu else '❌ No (CPU only)'}")
    print(f"🧠 SAM2 Models: ✅ Available")
    print(f"🎨 MODNet Models: {'✅ Success' if modnet_success else '❌ Failed'}")
    print(f"🖼️  LaMa Models: ✅ Ready (simplified implementation)")
    
    if modnet_success:
        print("\n🎉 Setup complete! You can now use:")
        print("   - High-quality AI Matting (MODNet)")
        if has_gpu:
            print("   - GPU acceleration")
        else:
            print("   - CPU processing (GPU acceleration available if CUDA is set up)")
        print("   - All SAM2 model variants")
        print("   - Enhanced inpainting (simplified LaMa)")
    else:
        print("\n⚠️ Some models failed to download. The app will fallback to:")
        print("   - Traditional guided filter matting")
        print("   - Basic OpenCV inpainting")
    
    print(f"\n🚀 Run the app with: python app_stable.py")
    print(f"🔧 Install additional AI models with:")
    print(f"   pip install simple-lama-inpainting  # For advanced inpainting")


if __name__ == "__main__":
    main() 