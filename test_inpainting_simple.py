#!/usr/bin/env python3
"""
AI图像补全功能简单测试
测试LAMA模型的基础功能
"""

import os
import numpy as np
from PIL import Image
import cv2

def test_inpainting_model():
    """测试补全模型的基础功能"""
    print("🧪 开始测试图像补全功能...")
    
    try:
        # 导入补全模型
        from inpainting_model import LAMAInpainter, simple_inpaint
        
        print("✅ 成功导入补全模型")
        
        # 创建测试图像 (256x256, 彩色)
        test_image = np.random.randint(0, 255, (256, 256, 3), dtype=np.uint8)
        test_image[100:150, 100:150] = [255, 0, 0]  # 添加一个红色方块
        
        # 创建测试遮罩 (白色区域将被补全)
        test_mask = np.zeros((256, 256), dtype=np.uint8)
        test_mask[120:130, 120:130] = 255  # 在红色方块中央创建小补全区域
        
        print("✅ 创建测试图像和遮罩")
        
        # 测试LAMA补全器
        print("🔧 测试LAMA补全器初始化...")
        inpainter = LAMAInpainter()
        print(f"✅ LAMA补全器初始化成功, 模型类型: {inpainter.model}")
        
        # 测试补全功能
        print("🎨 测试图像补全...")
        result = inpainter.inpaint(test_image, test_mask, method="telea")
        print(f"✅ 补全成功, 结果尺寸: {result.size}")
        
        # 测试简单补全函数
        print("🎨 测试简单补全函数...")
        result2 = simple_inpaint(test_image, test_mask, method="ns")
        print(f"✅ 简单补全成功, 结果尺寸: {result2.size}")
        
        # 保存测试结果
        os.makedirs("test_outputs", exist_ok=True)
        
        # 保存原图、遮罩和结果
        Image.fromarray(test_image).save("test_outputs/test_input_image.png")
        Image.fromarray(test_mask).save("test_outputs/test_input_mask.png")
        result.save("test_outputs/test_result_telea.png")
        result2.save("test_outputs/test_result_ns.png")
        
        print("✅ 测试结果已保存到 test_outputs/ 目录")
        print("🎉 所有测试通过！")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_complete_app():
    """测试完整应用的导入"""
    print("\n🧪 测试完整应用导入...")
    
    try:
        from app_inpainting_complete import AIImageInpaintingComplete
        
        print("✅ 成功导入完整应用类")
        
        # 尝试创建应用实例
        print("🔧 创建应用实例...")
        app = AIImageInpaintingComplete(model_size="tiny")
        
        print("✅ 应用实例创建成功")
        print(f"   - SAM模型: {app.model_size}")
        print(f"   - 当前模式: {app.mode}")
        print(f"   - 补全方法: {app.inpainting_method}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
        
    except Exception as e:
        print(f"❌ 创建应用实例失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始AI图像补全功能测试")
    print("=" * 50)
    
    # 测试补全模型
    model_test = test_inpainting_model()
    
    # 测试完整应用
    app_test = test_complete_app()
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print(f"   - 补全模型测试: {'✅ 通过' if model_test else '❌ 失败'}")
    print(f"   - 完整应用测试: {'✅ 通过' if app_test else '❌ 失败'}")
    
    if model_test and app_test:
        print("\n🎉 所有测试通过！您可以运行完整的补全应用了！")
        print("💡 使用命令: python app_inpainting_complete.py")
    else:
        print("\n⚠️  部分测试失败，请检查依赖和配置")
    
    return model_test and app_test


if __name__ == "__main__":
    main()