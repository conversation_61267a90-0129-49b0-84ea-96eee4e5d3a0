#!/usr/bin/env python3
"""
AI智能抠图工具 - 启动脚本
简化版启动脚本，自动检查依赖并启动应用
"""

import sys
import os
import subprocess
import importlib.util

def check_package(package_name):
    """检查包是否已安装"""
    spec = importlib.util.find_spec(package_name)
    return spec is not None

def install_package(package_name):
    """安装包"""
    subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])

def check_dependencies():
    """检查并安装依赖"""
    required_packages = [
        "torch",
        "torchvision", 
        "opencv-python",
        "Pillow",
        "gradio",
        "numpy",
        "matplotlib"
    ]
    
    missing_packages = []
    for package in required_packages:
        if not check_package(package):
            missing_packages.append(package)
    
    if missing_packages:
        print(f"📦 检测到缺失依赖包: {', '.join(missing_packages)}")
        print("正在自动安装...")
        
        try:
            for package in missing_packages:
                print(f"安装 {package}...")
                install_package(package)
            print("✅ 依赖安装完成！")
        except Exception as e:
            print(f"❌ 依赖安装失败: {e}")
            print("请手动运行: pip install -r requirements.txt")
            return False
    
    # 检查 SAM2
    if not check_package("sam2"):
        print("📦 正在安装 SAM2...")
        try:
            install_package("git+https://github.com/facebookresearch/sam2.git")
            print("✅ SAM2 安装完成！")
        except Exception as e:
            print(f"⚠️ SAM2 安装失败: {e}")
            print("请手动运行: pip install git+https://github.com/facebookresearch/sam2.git")
            print("或访问 https://github.com/facebookresearch/sam2 获取安装指南")
            return False
    
    return True

def main():
    """主函数"""
    print("🚀 AI智能抠图工具 - 启动中...")
    print("=" * 50)
    
    # 检查 Python 版本
    if sys.version_info < (3, 8):
        print("❌ Python 版本过低，需要 Python 3.8+")
        sys.exit(1)
    
    # 检查依赖
    print("🔍 检查依赖...")
    if not check_dependencies():
        print("❌ 依赖检查失败，请手动安装依赖后重试")
        sys.exit(1)
    
    # 检查 PyTorch
    try:
        import torch
        print(f"✅ PyTorch 版本: {torch.__version__}")
        print(f"✅ CUDA 可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            try:
                print(f"✅ CUDA 版本: {torch.version.cuda}")
            except:
                print("✅ CUDA 已启用")
        else:
            print("ℹ️ 将使用 CPU 模式（速度较慢）")
    except ImportError:
        print("❌ PyTorch 未正确安装")
        sys.exit(1)
    
    print("=" * 50)
    print("🎯 启动应用...")
    
    # 启动应用
    try:
        from app import main as app_main
        app_main()
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保 app.py 文件存在且无语法错误")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 用户取消，程序退出")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 