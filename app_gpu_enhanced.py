"""
AI Smart Cutout Tool - GPU Enhanced Version with Real AI Models
AI智能抠图工具 - GPU增强版本，集成真正的AI模型
"""

import gradio as gr
import numpy as np
from PIL import Image
import traceback
import os
import cv2
import time
import torch

# Import our custom modules
from sam_model import SAMPredictor
from matting_model import MODNetWrapper
from utils import (
    pil_to_numpy, numpy_to_pil, visualize_masks, visualize_masks_with_borders,
    find_mask_at_point, resize_image_for_display, merge_masks,
    apply_mask_to_image, create_cutout_image
)

class AIImageCutoutGPU:
    """AI图像抠图工具 - GPU增强版"""
    
    def __init__(self, sam_model_size: str = "tiny", use_gpu: bool = True):
        """
        初始化AI抠图工具
        
        Args:
            sam_model_size: SAM模型大小 ("tiny", "small", "base_plus", "large")
            use_gpu: 是否使用GPU
        """
        print("🚀 初始化AI智能抠图工具 - GPU增强版")
        print("=" * 60)
        
        self.use_gpu = use_gpu
        self.device_info = {}
        
        # Initialize models
        self._init_models(sam_model_size)
        
        # Image and mask storage
        self.current_image = None
        self.display_image = None
        self.current_masks = []
        self.selected_masks = []
        self.segmentation_image = None
        self.selection_mode = "single"  # "single" or "multiple"
        
        # Processing parameters
        self.use_matting = True
        self.trimap_size = 10
        self.matting_quality = "balanced"
        self.detail_enhancement = 1.0
        
        print("✅ AI智能抠图工具初始化完成！")
        print("=" * 60)
    
    def _init_models(self, sam_model_size: str):
        """初始化AI模型"""
        try:
            # Initialize SAM2 predictor
            print(f"🧠 Loading SAM2 {sam_model_size} model...")
            self.predictor = SAMPredictor(sam_model_size, use_gpu=self.use_gpu)
            
            # Get device info from SAM
            sam_info = self.predictor.get_model_info()
            self.device_info['sam'] = sam_info
            
            # Initialize Matting model
            print("🎨 Loading Matting model...")
            self.matting_model = MODNetWrapper(use_gpu=self.use_gpu)
            
            # Get device info from Matting
            matting_info = self.matting_model.get_model_info()
            self.device_info['matting'] = matting_info
            
            print(f"📊 模型信息:")
            print(f"   SAM2: {sam_info['model_size']} on {sam_info['device']}")
            print(f"   Matting: {matting_info['model_type']} on {matting_info['device']}")
            
            if self.use_gpu and torch.cuda.is_available():
                memory_info = self.predictor.get_memory_usage()
                if memory_info:
                    print(f"   GPU Memory: {memory_info.get('gpu_allocated', 0):.1f}GB allocated")
            
        except Exception as e:
            print(f"❌ Model initialization failed: {e}")
            traceback.print_exc()
            raise
    
    def get_device_status(self) -> str:
        """获取设备状态信息"""
        status = []
        
        # GPU状态
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            status.append(f"🚀 GPU: {gpu_name}")
        else:
            status.append("💻 Device: CPU only")
        
        # 模型状态
        sam_info = self.device_info.get('sam', {})
        matting_info = self.device_info.get('matting', {})
        
        status.append(f"🧠 SAM2: {sam_info.get('model_size', 'Unknown')} ({sam_info.get('device', 'Unknown')})")
        status.append(f"🎨 Matting: {matting_info.get('model_type', 'Unknown')}")
        
        return "\n".join(status)
    
    def upload_image(self, image):
        """上传并处理图像"""
        if image is None:
            return None, None, "请上传图像", gr.update(visible=False)
        
        try:
            start_time = time.time()
            
            # Convert and store original image
            if isinstance(image, Image.Image):
                self.current_image = pil_to_numpy(image)
            else:
                self.current_image = image
            
            # Resize for display (keep original for final processing)
            self.display_image = resize_image_for_display(self.current_image)
            
            # Set image for SAM predictor
            success = self.predictor.set_image(self.current_image)
            if not success:
                return None, None, "❌ 图像设置失败", gr.update(visible=False)
            
            # Generate masks automatically
            self.current_masks = self.predictor.predict_everything()
            
            if not self.current_masks:
                return None, None, "❌ 未检测到任何区域", gr.update(visible=False)
            
            # Clear previous selections
            self.selected_masks = []
            
            # Visualize masks
            self.segmentation_image = visualize_masks_with_borders(
                self.display_image, self.current_masks, self.selected_masks
            )
            
            processing_time = time.time() - start_time
            status = f"✅ 图像分析完成！检测到 {len(self.current_masks)} 个区域 ({processing_time:.2f}s)"
            
            return (
                numpy_to_pil(self.segmentation_image),
                None,
                status,
                gr.update(visible=False)
            )
            
        except Exception as e:
            error_msg = f"❌ 图片处理失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return None, None, error_msg, gr.update(visible=False)
    
    def on_image_click(self, evt: gr.SelectData) -> tuple:
        """处理图像点击事件"""
        if self.current_masks is None or len(self.current_masks) == 0:
            return None, None, "请先上传并分析图片", gr.update(visible=False)
        
        try:
            if evt is None or not hasattr(evt, 'index'):
                return None, None, "无效的点击事件", gr.update(visible=False)
            
            x, y = evt.index[0], evt.index[1]
            print(f"👆 Click at: ({x}, {y})")
            
            # Find clicked mask
            clicked_mask = find_mask_at_point(self.current_masks, (x, y))
            
            if clicked_mask is None:
                return None, None, "未找到点击区域", gr.update(visible=False)
            
            if self.selection_mode == "single":
                return self._process_single_selection(clicked_mask)
            else:
                return self._process_multiple_selection(clicked_mask)
                
        except Exception as e:
            error_msg = f"❌ 点击处理失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return None, None, error_msg, gr.update(visible=False)
    
    def _process_single_selection(self, clicked_mask) -> tuple:
        """处理单选模式"""
        try:
            start_time = time.time()
            
            # Generate cutout immediately
            result_image, output_path = self._generate_cutout_result([clicked_mask])
            
            processing_time = time.time() - start_time
            status = f"✅ 抠图完成！({processing_time:.2f}s)"
            
            if self.use_matting and self.matting_model.use_real_model:
                status += " | 使用 AI Matting 增强"
            elif self.use_matting:
                status += " | 使用 Matting 增强"
            
            return (
                numpy_to_pil(self.segmentation_image),
                result_image,
                status,
                gr.update(visible=True, value=output_path) if output_path else gr.update(visible=False)
            )
            
        except Exception as e:
            error_msg = f"❌ 抠图生成失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return None, None, error_msg, gr.update(visible=False)
    
    def _process_multiple_selection(self, clicked_mask) -> tuple:
        """处理多选模式"""
        try:
            # Check if mask already selected
            mask_already_selected = False
            for i, selected_mask in enumerate(self.selected_masks):
                if np.array_equal(selected_mask['segmentation'], clicked_mask['segmentation']):
                    self.selected_masks.pop(i)
                    mask_already_selected = True
                    break
            
            if not mask_already_selected:
                self.selected_masks.append(clicked_mask)
            
            # Update visualization
            self.segmentation_image = visualize_masks_with_borders(
                self.display_image, self.current_masks, self.selected_masks
            )
            
            status = f"已选择 {len(self.selected_masks)} 个区域"
            
            return (
                numpy_to_pil(self.segmentation_image),
                None,
                status,
                gr.update(visible=len(self.selected_masks) > 0)
            )
            
        except Exception as e:
            error_msg = f"❌ 多选处理失败: {str(e)}"
            print(error_msg)
            return None, None, error_msg, gr.update(visible=False)
    
    def _generate_cutout_result(self, masks) -> tuple:
        """生成抠图结果"""
        if not masks:
            return None, None
        
        try:
            # Merge multiple masks if necessary
            if len(masks) > 1:
                merged_mask = merge_masks([mask['segmentation'] for mask in masks])
            else:
                merged_mask = masks[0]['segmentation']
            
            # Resize mask to match original image
            if merged_mask.shape != self.current_image.shape[:2]:
                merged_mask = cv2.resize(
                    merged_mask.astype(np.uint8), 
                    (self.current_image.shape[1], self.current_image.shape[0]),
                    interpolation=cv2.INTER_NEAREST
                ).astype(bool)
            
            # Apply matting if enabled
            if self.use_matting:
                try:
                    # Use matting model for high-quality edges
                    matte = self.matting_model.predict(
                        self.current_image,
                        merged_mask.astype(np.uint8) * 255,
                        trimap_size=self.trimap_size,
                        quality=self.matting_quality
                    )
                    
                    # Enhance details if requested
                    if self.detail_enhancement != 1.0:
                        matte = self.matting_model.enhance_details(matte, self.detail_enhancement)
                    
                    # Create RGBA image with matting
                    from matting_model import create_rgba_with_matte
                    result_pil = create_rgba_with_matte(self.current_image, matte)
                    
                except Exception as e:
                    print(f"⚠️ Matting failed, using simple cutout: {e}")
                    result_pil = create_cutout_image(self.current_image, merged_mask)
            else:
                # Simple cutout without matting
                result_pil = create_cutout_image(self.current_image, merged_mask)
            
            # Save result
            timestamp = int(time.time())
            output_path = f"outputs/cutout_{timestamp}.png"
            os.makedirs("outputs", exist_ok=True)
            result_pil.save(output_path)
            
            return result_pil, output_path
            
        except Exception as e:
            print(f"❌ Cutout generation failed: {e}")
            traceback.print_exc()
            return None, None
    
    def export_selected(self):
        """导出选中的区域"""
        if not self.selected_masks:
            return None, "请先选择区域", None
        
        try:
            result_image, output_path = self._generate_cutout_result(self.selected_masks)
            
            if result_image is None:
                return None, "❌ 导出失败", None
            
            status = f"✅ 已导出 {len(self.selected_masks)} 个区域的合并结果"
            
            return (
                result_image,
                status,
                output_path if output_path and os.path.exists(output_path) else None
            )
            
        except Exception as e:
            error_msg = f"❌ 导出失败: {str(e)}"
            print(error_msg)
            return None, error_msg, None
    
    def clear_selection(self):
        """清空选择"""
        self.selected_masks = []
        if self.current_masks:
            self.segmentation_image = visualize_masks_with_borders(
                self.display_image, self.current_masks, self.selected_masks
            )
            return numpy_to_pil(self.segmentation_image), "✅ 已清空选择", gr.update(visible=False)
        return None, "请先上传图片", gr.update(visible=False)
    
    def change_sam_model(self, new_model_size: str):
        """切换SAM模型"""
        try:
            if new_model_size == self.predictor.model_size:
                return None, f"已在使用 {new_model_size} 模型", gr.update(visible=False)
            
            print(f"🔄 Switching to SAM2 {new_model_size} model...")
            
            success = self.predictor.switch_model(new_model_size)
            
            if success:
                # Re-analyze current image if available
                if self.current_image is not None:
                    return self.upload_image(numpy_to_pil(self.current_image))
                else:
                    status = f"✅ 已切换到 {new_model_size} 模型"
                    return None, status, gr.update(visible=False)
            else:
                return None, f"❌ 切换到 {new_model_size} 模型失败", gr.update(visible=False)
                
        except Exception as e:
            error_msg = f"❌ 模型切换失败: {str(e)}"
            print(error_msg)
            return None, error_msg, gr.update(visible=False)
    
    def update_matting_params_realtime(self, use_matting, trimap_size, matting_quality, detail_enhancement):
        """实时更新Matting参数"""
        self.use_matting = use_matting
        self.trimap_size = trimap_size
        self.matting_quality = matting_quality
        self.detail_enhancement = detail_enhancement
        
        # Re-generate result if in single mode and we have a selection
        if self.selection_mode == "single" and self.selected_masks:
            result_image, output_path = self._generate_cutout_result(self.selected_masks)
            if result_image:
                status = "✅ 参数已更新"
                return result_image, status, output_path if output_path and os.path.exists(output_path) else None
        
        return None, "✅ 参数已更新", None
    
    def update_selection_mode(self, mode):
        """更新选择模式"""
        self.selection_mode = mode
        self.selected_masks = []  # Clear selections when switching modes
        
        if self.current_masks:
            self.segmentation_image = visualize_masks_with_borders(
                self.display_image, self.current_masks, self.selected_masks
            )
            return numpy_to_pil(self.segmentation_image), f"✅ 已切换到{mode}模式", gr.update(visible=False)
        
        return None, f"✅ 已切换到{mode}模式", gr.update(visible=False)


def create_interface():
    """创建Gradio界面"""
    # Initialize the app with GPU support
    app = AIImageCutoutGPU(sam_model_size="tiny", use_gpu=True)
    
    # Custom CSS for better styling
    css = """
    .gradio-container {
        max-width: 1200px !important;
    }
    .device-status {
        background: linear-gradient(90deg, #4CAF50, #45a049);
        color: white;
        padding: 10px;
        border-radius: 5px;
        margin: 10px 0;
        font-family: monospace;
    }
    """
    
    with gr.Blocks(css=css, title="AI智能抠图工具 - GPU增强版") as interface:
        gr.Markdown("# 🚀 AI智能抠图工具 - GPU增强版")
        gr.Markdown("*基于SAM2和MODNet的智能图像分割与抠图工具*")
        
        # Device status
        with gr.Row():
            device_status = gr.Textbox(
                value=app.get_device_status(),
                label="🖥️ 设备状态",
                interactive=False,
                elem_classes=["device-status"]
            )
        
        with gr.Row():
            with gr.Column(scale=1):
                # Image upload
                input_image = gr.Image(
                    label="📤 上传图片",
                    type="pil",
                    height=400
                )
                
                # SAM model selection
                model_selector = gr.Dropdown(
                    choices=["tiny", "small", "base_plus", "large"],
                    value="tiny",
                    label="🧠 SAM2 模型",
                    info="更大的模型准确度更高但速度更慢"
                )
                
                # Selection mode
                selection_mode = gr.Radio(
                    choices=["single", "multiple"],
                    value="single",
                    label="🎯 选择模式",
                    info="单选：点击直接生成抠图；多选：选择多个区域后导出"
                )
                
                # Matting settings (collapsible)
                with gr.Accordion("🎨 Matting 设置", open=False):
                    use_matting = gr.Checkbox(
                        value=True,
                        label="启用 Matting 边缘优化",
                        info="使用AI模型优化抠图边缘"
                    )
                    
                    trimap_size = gr.Slider(
                        minimum=5,
                        maximum=30,
                        value=10,
                        step=1,
                        label="边缘羽化范围",
                        info="调整边缘过渡区域大小"
                    )
                    
                    matting_quality = gr.Radio(
                        choices=["fast", "balanced", "high"],
                        value="balanced",
                        label="处理质量",
                        info="高质量处理时间更长但效果更好"
                    )
                    
                    detail_enhancement = gr.Slider(
                        minimum=0.5,
                        maximum=2.0,
                        value=1.0,
                        step=0.1,
                        label="细节增强",
                        info="增强抠图细节，1.0为原始效果"
                    )
            
            with gr.Column(scale=2):
                # Segmentation display
                segmentation_output = gr.Image(
                    label="🎯 图像分割结果 (点击选择区域)",
                    type="pil",
                    height=400
                )
                
                # Result display
                result_output = gr.Image(
                    label="✨ 抠图结果",
                    type="pil",
                    height=400
                )
                
                # Status and controls
                with gr.Row():
                    status_text = gr.Textbox(
                        label="📊 状态",
                        interactive=False,
                        lines=2
                    )
                
                with gr.Row():
                    export_btn = gr.Button(
                        "📥 导出选中区域",
                        variant="primary",
                        visible=False
                    )
                    clear_btn = gr.Button(
                        "🗑️ 清空选择",
                        variant="secondary"
                    )
                
                # Download button
                download_btn = gr.File(
                    label="💾 下载抠图结果",
                    visible=False
                )
        
        # Event handlers
        
        # Image upload
        input_image.change(
            fn=app.upload_image,
            inputs=[input_image],
            outputs=[segmentation_output, result_output, status_text, download_btn]
        )
        
        # Image click
        segmentation_output.select(
            fn=app.on_image_click,
            outputs=[segmentation_output, result_output, status_text, download_btn]
        )
        
        # Model change
        model_selector.change(
            fn=app.change_sam_model,
            inputs=[model_selector],
            outputs=[segmentation_output, status_text, download_btn]
        )
        
        # Selection mode change
        selection_mode.change(
            fn=app.update_selection_mode,
            inputs=[selection_mode],
            outputs=[segmentation_output, status_text, export_btn]
        )
        
        # Real-time matting parameter updates
        for component in [use_matting, trimap_size, matting_quality, detail_enhancement]:
            component.change(
                fn=app.update_matting_params_realtime,
                inputs=[use_matting, trimap_size, matting_quality, detail_enhancement],
                outputs=[result_output, status_text, download_btn]
            )
        
        # Button clicks
        export_btn.click(
            fn=app.export_selected,
            outputs=[result_output, status_text, download_btn]
        )
        
        clear_btn.click(
            fn=app.clear_selection,
            outputs=[segmentation_output, status_text, export_btn]
        )
    
    return interface


if __name__ == "__main__":
    print("🚀 启动 AI智能抠图工具 - GPU增强版")
    print("=" * 60)
    
    # Create and launch interface
    interface = create_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    ) 