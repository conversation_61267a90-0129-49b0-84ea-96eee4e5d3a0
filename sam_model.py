"""
SAM2 Model Integration with GPU/CPU Support
集成SAM2模型，支持GPU和CPU回退
"""

import torch
import numpy as np
from PIL import Image
import cv2
import os
import requests
from typing import List, Dict, Any, Optional, Tuple, Union

# SAM2 imports
try:
    from sam2.build_sam import build_sam2
    from sam2.sam2_image_predictor import SAM2ImagePredictor
    from sam2.automatic_mask_generator import SAM2AutomaticMaskGenerator
    SAM2_AVAILABLE = True
except ImportError:
    print("⚠️ SAM2 not available. Install with: pip install git+https://github.com/facebookresearch/segment-anything-2.git")
    SAM2_AVAILABLE = False


class SAMPredictor:
    """SAM2 预测器封装类，支持GPU和CPU"""
    
    def __init__(self, model_size: str = "tiny", use_gpu: bool = True, checkpoint_path: Optional[str] = None):
        """
        初始化 SAM2 预测器
        
        Args:
            model_size: 模型大小 ("tiny", "small", "base_plus", "large")
            use_gpu: 是否尝试使用GPU
            checkpoint_path: 自定义模型权重路径
        """
        self.model_size = model_size
        self.device = self._get_device()
        self.predictor = None
        self.mask_generator = None
        self.current_image = None
        self.image_embeddings = None
        
        # Model configuration
        self.model_cfg = self._get_model_config(model_size)
        self.checkpoint_path = checkpoint_path or self._get_checkpoint_path(model_size)
        
        # Load model
        self._load_model()
    
    def _get_device(self) -> torch.device:
        """获取运行设备，优先使用GPU"""
        if torch.cuda.is_available():
            try:
                # 测试GPU是否可用
                test_tensor = torch.randn(1, 3, 512, 512).cuda()
                del test_tensor
                torch.cuda.empty_cache()
                
                device = torch.device('cuda')
                gpu_name = torch.cuda.get_device_name(0)
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
                print(f"🚀 SAM2 using GPU: {gpu_name} ({gpu_memory:.1f}GB)")
                return device
            except Exception as e:
                print(f"⚠️ GPU test failed for SAM2: {e}")
                print("🔄 SAM2 falling back to CPU")
                return torch.device('cpu')
        else:
            print("💻 SAM2 using CPU (CUDA not available)")
            return torch.device('cpu')
    
    def _get_model_config(self, model_size: str) -> str:
        """获取模型配置文件路径"""
        config_map = {
            "tiny": "sam2_hiera_t.yaml",
            "small": "sam2_hiera_s.yaml", 
            "base_plus": "sam2_hiera_b+.yaml",
            "large": "sam2_hiera_l.yaml"
        }
        
        config_name = config_map.get(model_size, "sam2_hiera_t.yaml")
        
        # Try to find config in common locations
        possible_paths = [
            f"sam2/configs/sam2/{config_name}",
            f"configs/sam2/{config_name}",
            f"sam2_configs/{config_name}",
            config_name
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                print(f"📋 Found SAM2 config: {path}")
                return path
        
        print(f"⚠️ Config file not found, using: {config_name}")
        return config_name
    
    def _get_checkpoint_path(self, model_size: str) -> str:
        """获取模型权重文件路径"""
        checkpoint_map = {
            "tiny": "sam2_tiny.pt",
            "small": "sam2_small.pt",
            "base_plus": "sam2_base_plus.pt", 
            "large": "sam2_large.pt"
        }
        
        checkpoint_name = checkpoint_map.get(model_size, "sam2_tiny.pt")
        
        # Try to find checkpoint in common locations
        possible_paths = [
            f"checkpoints/{checkpoint_name}",
            f"models/{checkpoint_name}",
            f"sam2_checkpoints/{checkpoint_name}",
            checkpoint_name
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                file_size = os.path.getsize(path) / 1024 / 1024
                print(f"🔍 Found SAM2 checkpoint: {path} ({file_size:.1f}MB)")
                return path
        
        print(f"❌ SAM2 checkpoint not found: {checkpoint_name}")
        print("💡 Run 'python download_models.py' to download SAM2 models")
        return checkpoint_name
    
    def _load_model(self) -> bool:
        """加载 SAM2 模型"""
        if not SAM2_AVAILABLE:
            print("❌ SAM2 not available. Please install SAM2")
            return False
        
        try:
            # Check if checkpoint exists
            if not os.path.exists(self.checkpoint_path):
                print(f"❌ Checkpoint not found: {self.checkpoint_path}")
                return False
            
            print(f"🤖 Loading SAM2 {self.model_size} model...")
            print(f"📁 Config: {self.model_cfg}")
            print(f"📁 Checkpoint: {self.checkpoint_path}")
            print(f"🖥️ Device: {self.device}")
            
            # Build model
            sam2_model = build_sam2(self.model_cfg, self.checkpoint_path, device=self.device)
            
            # Create predictor
            self.predictor = SAM2ImagePredictor(sam2_model)
            
            # Create automatic mask generator  
            self.mask_generator = SAM2AutomaticMaskGenerator(
                model=sam2_model,
                points_per_side=32,
                points_per_batch=64,
                pred_iou_thresh=0.8,
                stability_score_thresh=0.85,
                stability_score_offset=1.0,
                box_nms_thresh=0.7,
                crop_n_layers=0,
                crop_nms_thresh=0.7,
                crop_overlap_ratio=512 / 1500,
                crop_n_points_downscale_factor=1,
                point_grids=None,
                min_mask_region_area=100,
                output_mode='binary_mask',
            )
            
            print(f"✅ SAM2 {self.model_size} model loaded successfully on {self.device}!")
            return True
            
        except Exception as e:
            print(f"❌ Failed to load SAM2 model: {e}")
            print("💡 Try downloading models with: python download_ai_models.py")
            return False
    
    def set_image(self, image: Union[np.ndarray, Image.Image]) -> bool:
        """
        设置要处理的图像
        
        Args:
            image: 输入图像 (numpy array 或 PIL Image)
            
        Returns:
            是否成功设置图像
        """
        try:
            # Convert PIL to numpy if needed
            if isinstance(image, Image.Image):
                image = np.array(image)
            
            # Ensure RGB format
            if len(image.shape) == 3 and image.shape[2] == 3:
                # Already RGB
                processed_image = image
            elif len(image.shape) == 3 and image.shape[2] == 4:
                # RGBA to RGB
                processed_image = image[:, :, :3]
            else:
                # Grayscale to RGB
                processed_image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
            
            self.current_image = processed_image
            
            if self.predictor is not None:
                self.predictor.set_image(processed_image)
                print(f"📸 Image set successfully. Shape: {processed_image.shape}")
                return True
            else:
                print("❌ Predictor not loaded")
                return False
                
        except Exception as e:
            print(f"❌ Failed to set image: {e}")
            return False
    
    def predict_point(self, point: Tuple[int, int], point_label: int = 1) -> List[Dict[str, Any]]:
        """
        基于点击预测分割
        
        Args:
            point: 点击坐标 (x, y)
            point_label: 点标签 (1=前景, 0=背景)
            
        Returns:
            预测结果列表
        """
        if self.predictor is None:
            print("❌ Predictor not loaded")
            return []
        
        try:
            input_points = np.array([[point[0], point[1]]])
            input_labels = np.array([point_label])
            
            masks, scores, logits = self.predictor.predict(
                point_coords=input_points,
                point_labels=input_labels,
                multimask_output=True,
            )
            
            # Format results
            results = []
            for i, (mask, score) in enumerate(zip(masks, scores)):
                results.append({
                    'segmentation': mask,
                    'area': int(np.sum(mask)),
                    'bbox': self._mask_to_bbox(mask),
                    'predicted_iou': float(score),
                    'point_coords': [point],
                    'stability_score': float(score),
                    'crop_box': [0, 0, mask.shape[1], mask.shape[0]]
                })
            
            return results
            
        except Exception as e:
            print(f"❌ Point prediction failed: {e}")
            return []
    
    def predict_everything(self) -> List[Dict[str, Any]]:
        """
        自动分割整个图像
        
        Returns:
            所有检测到的mask列表
        """
        if self.mask_generator is None:
            print("❌ Mask generator not loaded")
            return []
        
        if self.current_image is None:
            print("❌ No image set")
            return []
        
        try:
            print("🔍 Generating masks...")
            
            # 确保图像已经设置到predictor
            if hasattr(self.mask_generator, '_predictor'):
                self.mask_generator._predictor.set_image(self.current_image)
            elif hasattr(self.mask_generator, 'predictor'):
                self.mask_generator.predictor.set_image(self.current_image)
            
            masks = self.mask_generator.generate(self.current_image)
            
            print(f"✅ Generated {len(masks)} masks")
            return masks
            
        except Exception as e:
            print(f"❌ Automatic mask generation failed: {e}")
            # 如果自动生成失败，尝试网格采样方法
            print("🔄 Trying grid sampling method...")
            try:
                masks = self._generate_grid_masks()
                print(f"✅ Generated {len(masks)} masks using grid sampling")
                return masks
            except Exception as e2:
                print(f"❌ Grid sampling also failed: {e2}")
                return []
    
    def _generate_grid_masks(self) -> List[Dict[str, Any]]:
        """
        使用网格采样方法生成masks作为备用方案
        """
        if self.predictor is None or self.current_image is None:
            return []
        
        h, w = self.current_image.shape[:2]
        masks = []
        
        # 创建网格点
        points_per_side = 16  # 减少点数以提高速度
        x_coords = np.linspace(w * 0.1, w * 0.9, points_per_side)
        y_coords = np.linspace(h * 0.1, h * 0.9, points_per_side)
        
        for y in y_coords:
            for x in x_coords:
                point = np.array([[x, y]])
                point_labels = np.array([1])
                
                try:
                    # 使用predictor预测单个点
                    masks_pred, scores, _ = self.predictor.predict(
                        point_coords=point,
                        point_labels=point_labels,
                        multimask_output=True
                    )
                    
                    # 选择最佳mask
                    best_idx = np.argmax(scores)
                    mask = masks_pred[best_idx]
                    
                    # 检查mask是否有效
                    area = np.sum(mask)
                    if area > 100:  # 最小面积阈值
                        masks.append({
                            'segmentation': mask,
                            'area': int(area),
                            'stability_score': float(scores[best_idx]),
                            'predicted_iou': float(scores[best_idx])
                        })
                        
                except Exception:
                    continue
        
        # 去除重复的masks
        unique_masks = []
        for mask in masks:
            is_unique = True
            for unique_mask in unique_masks:
                # 计算IOU
                intersection = np.logical_and(mask['segmentation'], unique_mask['segmentation']).sum()
                union = np.logical_or(mask['segmentation'], unique_mask['segmentation']).sum()
                iou = intersection / (union + 1e-10)
                
                if iou > 0.8:  # 重复阈值
                    is_unique = False
                    break
            
            if is_unique:
                unique_masks.append(mask)
        
        return unique_masks
    
    def _mask_to_bbox(self, mask: np.ndarray) -> List[int]:
        """将mask转换为边界框"""
        pos = np.where(mask)
        if len(pos[0]) == 0:
            return [0, 0, 0, 0]
        
        xmin = int(np.min(pos[1]))
        xmax = int(np.max(pos[1]))
        ymin = int(np.min(pos[0]))
        ymax = int(np.max(pos[0]))
        
        return [xmin, ymin, xmax - xmin, ymax - ymin]
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_size": self.model_size,
            "device": str(self.device),
            "config_path": self.model_cfg,
            "checkpoint_path": self.checkpoint_path,
            "predictor_loaded": self.predictor is not None,
            "mask_generator_loaded": self.mask_generator is not None,
            "sam2_available": SAM2_AVAILABLE,
            "current_image_shape": self.current_image.shape if self.current_image is not None else None
        }
    
    def switch_model(self, new_model_size: str) -> bool:
        """
        切换到不同大小的模型
        
        Args:
            new_model_size: 新的模型大小
            
        Returns:
            是否成功切换
        """
        if new_model_size == self.model_size:
            print(f"✅ Already using {new_model_size} model")
            return True
        
        print(f"🔄 Switching from {self.model_size} to {new_model_size} model...")
        
        # Update configuration
        old_size = self.model_size
        self.model_size = new_model_size
        self.model_cfg = self._get_model_config(new_model_size)
        self.checkpoint_path = self._get_checkpoint_path(new_model_size)
        
        # Try to load new model
        if self._load_model():
            # If successful and we had an image, re-set it
            if self.current_image is not None:
                temp_image = self.current_image.copy()
                self.set_image(temp_image)
            print(f"✅ Successfully switched to {new_model_size} model")
            return True
        else:
            # Revert on failure
            print(f"❌ Failed to switch to {new_model_size}, reverting to {old_size}")
            self.model_size = old_size
            self.model_cfg = self._get_model_config(old_size)
            self.checkpoint_path = self._get_checkpoint_path(old_size)
            self._load_model()
            return False
    
    def clear_cache(self):
        """清理GPU缓存"""
        if self.device.type == 'cuda':
            torch.cuda.empty_cache()
            print("🧹 GPU cache cleared")
    
    def get_memory_usage(self) -> Dict[str, float]:
        """获取内存使用情况"""
        info = {}
        
        if self.device.type == 'cuda':
            info['gpu_allocated'] = torch.cuda.memory_allocated(self.device) / 1024**3
            info['gpu_cached'] = torch.cuda.memory_reserved(self.device) / 1024**3
            info['gpu_total'] = torch.cuda.get_device_properties(self.device).total_memory / 1024**3
        
        return info


def download_sam2_model(model_size: str = "tiny") -> bool:
    """
    下载SAM2模型文件
    
    Args:
        model_size: 模型大小
        
    Returns:
        是否下载成功
    """
    model_urls = {
        "tiny": "https://dl.fbaipublicfiles.com/segment_anything_2/092824/sam2_hiera_tiny.pt",
        "small": "https://dl.fbaipublicfiles.com/segment_anything_2/092824/sam2_hiera_small.pt",
        "base_plus": "https://dl.fbaipublicfiles.com/segment_anything_2/092824/sam2_hiera_base_plus.pt",
        "large": "https://dl.fbaipublicfiles.com/segment_anything_2/092824/sam2_hiera_large.pt"
    }
    
    if model_size not in model_urls:
        print(f"❌ Unknown model size: {model_size}")
        return False
    
    url = model_urls[model_size]
    filename = f"sam2_{model_size}.pt"
    filepath = os.path.join("checkpoints", filename)
    
    # Check if already exists
    if os.path.exists(filepath):
        file_size = os.path.getsize(filepath) / 1024 / 1024
        print(f"✅ Model already exists: {filepath} ({file_size:.1f}MB)")
        return True
    
    # Create directory
    os.makedirs("checkpoints", exist_ok=True)
    
    try:
        print(f"📥 Downloading SAM2 {model_size} model...")
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        
        with open(filepath, 'wb') as f:
            downloaded = 0
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        progress = downloaded / total_size * 100
                        print(f"\r📥 Progress: {progress:.1f}%", end='', flush=True)
        
        print(f"\n✅ Downloaded: {filepath}")
        return True
        
    except Exception as e:
        print(f"\n❌ Download failed: {e}")
        if os.path.exists(filepath):
            os.remove(filepath)
        return False 