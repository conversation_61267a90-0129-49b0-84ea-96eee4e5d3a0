#!/usr/bin/env python3
"""
AI智能抠图工具 - 完整功能测试脚本
测试所有模型、GPU状态和功能是否正常
"""

import sys
import os
import traceback
import time
import numpy as np
from PIL import Image
import torch

def test_gpu_status():
    """测试GPU状态"""
    print("=" * 60)
    print("🔍 测试GPU状态")
    print("=" * 60)
    
    try:
        print(f"✅ PyTorch 版本: {torch.__version__}")
        print(f"✅ CUDA 可用: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"✅ CUDA 版本: {torch.version.cuda}")
            print(f"✅ GPU 数量: {torch.cuda.device_count()}")
            
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"✅ GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
                
            # 测试GPU计算
            device = torch.device("cuda")
            x = torch.randn(1000, 1000).to(device)
            y = torch.randn(1000, 1000).to(device)
            start_time = time.time()
            z = torch.mm(x, y)
            gpu_time = time.time() - start_time
            print(f"✅ GPU 计算测试: {gpu_time:.3f}秒")
            
        else:
            print("ℹ️ 使用CPU模式")
            # 测试CPU计算
            x = torch.randn(1000, 1000)
            y = torch.randn(1000, 1000)
            start_time = time.time()
            z = torch.mm(x, y)
            cpu_time = time.time() - start_time
            print(f"✅ CPU 计算测试: {cpu_time:.3f}秒")
            
        return True
        
    except Exception as e:
        print(f"❌ GPU测试失败: {e}")
        traceback.print_exc()
        return False

def test_sam_model():
    """测试SAM模型"""
    print("\n" + "=" * 60)
    print("🔍 测试SAM模型")
    print("=" * 60)
    
    try:
        from sam_model import SAMPredictor
        
        # 测试不同大小的模型
        model_sizes = ["tiny", "small", "base", "large"]
        
        for size in model_sizes:
            print(f"\n🧪 测试 SAM2 {size} 模型...")
            try:
                predictor = SAMPredictor(model_size=size)
                print(f"✅ SAM2 {size} 模型加载成功")
                
                # 创建测试图像
                test_image = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
                predictor.set_image(test_image)
                print(f"✅ SAM2 {size} 图像设置成功")
                
                # 测试预测
                masks = predictor.predict_everything()
                print(f"✅ SAM2 {size} 预测成功，生成 {len(masks)} 个mask")
                
            except Exception as e:
                print(f"❌ SAM2 {size} 模型测试失败: {e}")
                
        return True
        
    except Exception as e:
        print(f"❌ SAM模型测试失败: {e}")
        traceback.print_exc()
        return False

def test_matting_model():
    """测试抠图模型"""
    print("\n" + "=" * 60)
    print("🔍 测试抠图模型")
    print("=" * 60)
    
    try:
        from matting_model import MODNetWrapper, create_rgba_with_matte
        
        print("🧪 测试 MODNet 模型...")
        matting_model = MODNetWrapper()
        print("✅ MODNet 模型加载成功")
        
        # 创建测试图像和mask
        test_image = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
        test_mask = np.random.randint(0, 255, (512, 512), dtype=np.uint8)
        
        # 测试抠图
        result = matting_model.predict(test_image, test_mask)
        print("✅ MODNet 抠图处理成功")
        
        # 测试RGBA创建
        rgba_image = create_rgba_with_matte(test_image, result)
        print("✅ RGBA图像创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 抠图模型测试失败: {e}")
        traceback.print_exc()
        return False

def test_inpainting_model():
    """测试修复模型"""
    print("\n" + "=" * 60)
    print("🔍 测试修复模型")
    print("=" * 60)
    
    try:
        from inpainting_model import LAMAInpainter
        
        print("🧪 测试 LaMa 修复模型...")
        inpainter = LAMAInpainter()
        print("✅ LaMa 修复模型加载成功")
        
        # 创建测试图像和mask
        test_image = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
        test_mask = np.random.randint(0, 255, (512, 512), dtype=np.uint8)
        
        # 测试修复
        result = inpainter.inpaint(test_image, test_mask)
        print("✅ LaMa 修复处理成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复模型测试失败: {e}")
        traceback.print_exc()
        return False

def test_utils():
    """测试工具函数"""
    print("\n" + "=" * 60)
    print("🔍 测试工具函数")
    print("=" * 60)
    
    try:
        from utils import (
            pil_to_numpy, numpy_to_pil, visualize_masks, 
            resize_image_for_display, create_cutout_image
        )
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
        
        # 测试PIL转换
        pil_image = numpy_to_pil(test_image)
        numpy_image = pil_to_numpy(pil_image)
        print("✅ PIL转换测试成功")
        
        # 测试图像缩放
        resized = resize_image_for_display(test_image)
        print("✅ 图像缩放测试成功")
        
        # 测试mask可视化
        test_mask = np.random.randint(0, 255, (512, 512), dtype=np.uint8)
        visualized = visualize_masks(test_image, [test_mask])
        print("✅ Mask可视化测试成功")
        
        # 测试抠图创建
        cutout = create_cutout_image(test_image, test_mask)
        print("✅ 抠图创建测试成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 工具函数测试失败: {e}")
        traceback.print_exc()
        return False

def test_gradio_interface():
    """测试Gradio界面"""
    print("\n" + "=" * 60)
    print("🔍 测试Gradio界面")
    print("=" * 60)
    
    try:
        import gradio as gr
        
        print("✅ Gradio 导入成功")
        
        # 测试简单的Gradio界面
        def test_function(image):
            if image is not None:
                return "测试成功"
            return "请上传图像"
        
        demo = gr.Interface(
            fn=test_function,
            inputs=gr.Image(),
            outputs=gr.Textbox(),
            title="测试界面"
        )
        
        print("✅ Gradio 界面创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ Gradio界面测试失败: {e}")
        traceback.print_exc()
        return False

def test_model_files():
    """测试模型文件"""
    print("\n" + "=" * 60)
    print("🔍 测试模型文件")
    print("=" * 60)
    
    model_files = {
        "SAM2 Tiny": "checkpoints/sam2_tiny.pt",
        "SAM2 Small": "checkpoints/sam2_small.pt", 
        "SAM2 Base": "checkpoints/sam2_base_plus.pt",
        "SAM2 Large": "checkpoints/sam2_large.pt",
        "MODNet": "checkpoints/modnet_photographic_portrait_matting.pth"
    }
    
    all_files_exist = True
    
    for model_name, file_path in model_files.items():
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
            print(f"✅ {model_name}: {file_path} ({file_size:.1f}MB)")
        else:
            print(f"❌ {model_name}: {file_path} (文件不存在)")
            all_files_exist = False
    
    return all_files_exist

def main():
    """主测试函数"""
    print("🚀 AI智能抠图工具 - 完整功能测试")
    print("=" * 60)
    
    test_results = {}
    
    # 测试GPU状态
    test_results["GPU"] = test_gpu_status()
    
    # 测试模型文件
    test_results["Model Files"] = test_model_files()
    
    # 测试SAM模型
    test_results["SAM Model"] = test_sam_model()
    
    # 测试抠图模型
    test_results["Matting Model"] = test_matting_model()
    
    # 测试修复模型
    test_results["Inpainting Model"] = test_inpainting_model()
    
    # 测试工具函数
    test_results["Utils"] = test_utils()
    
    # 测试Gradio界面
    test_results["Gradio Interface"] = test_gradio_interface()
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！系统功能完整")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
    print("=" * 60)

if __name__ == "__main__":
    main() 