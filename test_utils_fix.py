import numpy as np
import cv2
from utils import visualize_masks

def test_visualize_masks():
    """测试visualize_masks函数是否修复了尺寸问题"""
    print("🧪 测试visualize_masks函数...")
    
    # 创建测试图像
    image = np.random.randint(0, 255, (800, 600, 3), dtype=np.uint8)
    
    # 创建不同尺寸的masks
    masks = []
    
    # Mask 1: 正确尺寸
    mask1 = np.zeros((800, 600), dtype=bool)
    mask1[100:300, 100:400] = True
    masks.append({'segmentation': mask1})
    
    # Mask 2: 错误尺寸 (1920x1080)
    mask2 = np.zeros((1920, 1080), dtype=bool)
    mask2[200:600, 200:800] = True
    masks.append({'segmentation': mask2})
    
    # Mask 3: 另一个错误尺寸 (400x300)
    mask3 = np.zeros((400, 300), dtype=bool)
    mask3[50:150, 50:200] = True
    masks.append({'segmentation': mask3})
    
    try:
        # 测试visualize_masks函数
        result = visualize_masks(image, masks)
        print("✅ visualize_masks测试通过！")
        print(f"   输入图像尺寸: {image.shape}")
        print(f"   输出图像尺寸: {result.shape}")
        print(f"   处理了 {len(masks)} 个masks")
        return True
    except Exception as e:
        print(f"❌ visualize_masks测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_visualize_masks()
    if success:
        print("\n🎉 修复验证成功！")
    else:
        print("\n💥 修复验证失败！") 