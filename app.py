import gradio as gr
import numpy as np
from PIL import Image
import traceback
import tempfile
import os
import cv2
import time

# Import our custom modules
from sam_model import SAMPredictor
from matting_model import MODNetWrapper, create_rgba_with_matte
from inpainting_model import LAMAInpainter
from utils import (
    pil_to_numpy, numpy_to_pil, visualize_masks, visualize_masks_with_borders,
    find_mask_at_point, filter_masks_by_area, resize_image_for_display, merge_masks,
    create_inpainting_mask, visualize_inpainting_preview, prepare_image_for_inpainting,
    apply_mask_to_image, create_cutout_image
)

class AIImageCutoutComplete:
    def __init__(self, model_size: str = "tiny"):
        """
        Initialize the AI Image Cutout application with complete functionality
        """
        self.model_size = model_size
        self.predictor = None
        self.matting_model = None
        self.inpainting_model = None
        self.current_image = None  # 原始图像
        self.display_image = None  # 调整后的显示图像
        self.current_masks = []
        self.selected_masks = []  # 存储选中的区域
        self.segmentation_image = None
        self.last_cutout_file = None
        self.last_inpainted_file = None
        self.selection_mode = "single"  # "single" or "multiple"
        
        # 保存最后选中的mask，用于参数更新时重新生成
        self.last_clicked_mask = None
        
        # Matting 参数
        self.use_matting = True
        self.trimap_size = 10
        self.matting_quality = "balanced"
        self.detail_enhancement = 1.0
        
        # Inpainting 参数
        self.use_inpainting = False
        self.inpainting_preview_alpha = 0.7
        self.inpainting_method = "telea"
        
        # Initialize models
        self._init_models()
    
    def _init_models(self):
        """Initialize all models"""
        try:
            # Initialize SAM model
            print(f"🚀 Loading SAM2 {self.model_size} model...")
            self.predictor = SAMPredictor(model_size=self.model_size)
            print("✅ SAM model loaded successfully!")
            
            # Initialize Matting model
            print("🎨 Loading Matting model...")
            self.matting_model = MODNetWrapper()
            print("✅ Matting model loaded successfully!")
            
            # Initialize Inpainting model
            print("🖼️ Loading LaMa inpainting model...")
            self.inpainting_model = LAMAInpainter()
            print("✅ LaMa inpainting model ready!")
            
            return True
            
        except Exception as e:
            print(f"❌ Model initialization error: {e}")
            traceback.print_exc()
            return False
    
    def upload_image(self, image: Image.Image) -> tuple:
        """Handle image upload and run SAM prediction"""
        if image is None:
            return None, "请上传图片"
        
        try:
            # Convert and store image
            self.current_image = pil_to_numpy(image)
            print(f"📸 Image set successfully. Shape: {self.current_image.shape}")
            
            # Resize for display if needed
            self.display_image = resize_image_for_display(self.current_image)
            
            # Clear previous results
            self.current_masks = []
            self.selected_masks = []
            self.last_clicked_mask = None
            
            # Run SAM prediction
            print("🔍 Generating masks...")
            if self.predictor and self.display_image is not None:
                # Set image for prediction
                self.predictor.set_image(self.display_image)
                
                # Generate masks using predict_everything
                self.current_masks = self.predictor.predict_everything()
                print(f"✅ Generated {len(self.current_masks)} masks")
                
                # Visualize masks
                if self.current_masks:
                    self.segmentation_image = visualize_masks_with_borders(
                        self.display_image, self.current_masks, []
                    )
                    return numpy_to_pil(self.segmentation_image), f"分析完成！检测到 {len(self.current_masks)} 个可选区域，点击选择"
                else:
                    return image, "未检测到明显区域，请尝试其他图片"
            else:
                return image, "模型未正确加载"
                
        except Exception as e:
            error_msg = f"图片处理失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return image, error_msg
    
    def on_image_click(self, evt: gr.SelectData) -> tuple:
        """Handle click on the segmentation image"""
        if self.current_masks is None or len(self.current_masks) == 0:
            return None, None, "请先上传并分析图片", None
        
        try:
            # 确保evt不为None且有index属性
            if evt is None or not hasattr(evt, 'index'):
                return None, None, "无效的点击事件", None
                
            # 获取点击坐标
            x, y = evt.index[0], evt.index[1]
            print(f"👆 Click at: ({x}, {y})")
            
            # Find mask at clicked point - 使用正确的参数格式
            clicked_mask = find_mask_at_point(self.current_masks, (x, y))
            
            if clicked_mask is None:
                return None, None, "未找到可选区域，请点击其他位置", None
            
            if self.selection_mode == "single":
                # Single selection mode
                self.selected_masks = [clicked_mask]
                self.last_clicked_mask = clicked_mask
                
                # Update visualization
                self.segmentation_image = visualize_masks_with_borders(
                    self.display_image, self.current_masks, self.selected_masks
                )
                
                # Generate result based on mode
                if self.use_inpainting:
                    # Inpainting mode - show preview
                    preview_image = visualize_inpainting_preview(
                        self.display_image, self.selected_masks, self.inpainting_preview_alpha
                    )
                    return (
                        numpy_to_pil(self.segmentation_image),
                        numpy_to_pil(preview_image),
                        "补全模式：红色区域将被补全",
                        None
                    )
                else:
                    # Cutout mode - generate cutout
                    return self._generate_cutout_result()
            
            else:
                # Multiple selection mode
                # 检查是否已经选择 - 使用ID或其他唯一标识符比较
                mask_already_selected = False
                for i, selected_mask in enumerate(self.selected_masks):
                    if np.array_equal(selected_mask['segmentation'], clicked_mask['segmentation']):
                        # Remove if already selected
                        self.selected_masks.pop(i)
                        mask_already_selected = True
                        break
                
                if mask_already_selected:
                    status = f"取消选择区域，当前已选择 {len(self.selected_masks)} 个区域"
                else:
                    # Add to selection
                    self.selected_masks.append(clicked_mask)
                    status = f"添加到选择，当前已选择 {len(self.selected_masks)} 个区域"
                
                # Update visualization
                self.segmentation_image = visualize_masks_with_borders(
                    self.display_image, self.current_masks, self.selected_masks
                )
                
                if self.use_inpainting:
                    # Inpainting mode - show preview
                    if self.selected_masks:
                        preview_image = visualize_inpainting_preview(
                            self.display_image, self.selected_masks, self.inpainting_preview_alpha
                        )
                        return (
                            numpy_to_pil(self.segmentation_image),
                            numpy_to_pil(preview_image),
                            status + "（红色区域将被补全）",
                            None
                        )
                    else:
                        return (
                            numpy_to_pil(self.segmentation_image),
                            None,
                            status,
                            None
                        )
                else:
                    # Cutout mode
                    return (
                        numpy_to_pil(self.segmentation_image),
                        None,
                        status,
                        None
                    )
                
        except Exception as e:
            error_msg = f"点击处理失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return None, None, error_msg, None
    
    def _generate_cutout_result(self) -> tuple:
        """Generate cutout result for selected masks"""
        try:
            if not self.selected_masks:
                return None, None, "请先选择要抠出的区域", None
            
            # Merge selected masks
            merged_mask = merge_masks([mask['segmentation'] for mask in self.selected_masks])
            
            # Resize mask to match original image if needed
            original_h, original_w = self.current_image.shape[:2]
            display_h, display_w = self.display_image.shape[:2]
            
            if original_h != display_h or original_w != display_w:
                merged_mask = cv2.resize(
                    merged_mask.astype(np.uint8), 
                    (original_w, original_h), 
                    interpolation=cv2.INTER_NEAREST
                ).astype(bool)
            
            # Create cutout image
            if self.use_matting and self.matting_model:
                # Use matting for better edges
                print("🎨 Using Matting for high-quality edges...")
                matte = self.matting_model.predict(
                    self.current_image,
                    merged_mask,
                    trimap_size=self.trimap_size,
                    quality=self.matting_quality
                )
                
                # Apply detail enhancement
                if self.detail_enhancement != 1.0:
                    matte = self.matting_model.enhance_details(matte, self.detail_enhancement)
                
                # Create RGBA image with matte - 使用正确的参数
                cutout_image = create_rgba_with_matte(self.current_image, matte)
            else:
                # Simple mask application - 使用utils中的函数
                print("✂️ Using simple mask application...")
                cutout_image = create_cutout_image(
                    self.current_image, 
                    merged_mask,
                    edge_method="smooth",
                    smooth_strength=0.5,
                    feather_radius=2
                )
            
            # Create output file
            timestamp = int(time.time())
            output_filename = f"cutout_{timestamp}.png"
            output_path = os.path.join("outputs", output_filename)
            
            # Ensure output directory exists
            os.makedirs("outputs", exist_ok=True)
            
            # Save cutout result
            cutout_image.save(output_path, 'PNG')
            self.last_cutout_file = output_path
            
            status_msg = f"✅ 抠图完成！"
            if self.use_matting:
                status_msg += f" | 使用 Matting 增强"
            
            return (
                numpy_to_pil(self.segmentation_image),
                cutout_image,
                status_msg,
                output_path if os.path.exists(output_path) else None
            )
            
        except Exception as e:
            error_msg = f"抠图生成失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return None, None, error_msg, None
    
    def generate_inpainting(self) -> tuple:
        """Generate image completion using inpainting"""
        try:
            if not self.selected_masks:
                return None, "请先选择要补全的区域", None
            
            print("🖼️ Starting image inpainting...")
            
            # Create mask for inpainting - 需要调整到原图尺寸
            original_h, original_w = self.current_image.shape[:2]
            display_h, display_w = self.display_image.shape[:2]
            
            # 先创建显示尺寸的mask
            display_mask = create_inpainting_mask(self.selected_masks, self.display_image.shape[:2])
            
            # 如果尺寸不同，调整到原图尺寸
            if original_h != display_h or original_w != display_w:
                mask = cv2.resize(
                    display_mask, 
                    (original_w, original_h), 
                    interpolation=cv2.INTER_NEAREST
                )
            else:
                mask = display_mask
            
            # Prepare image for inpainting
            image_pil = prepare_image_for_inpainting(self.current_image)
            
            # Convert mask to PIL format for inpainting
            mask_pil = Image.fromarray(mask)
            
            # Perform inpainting
            completed_image = self.inpainting_model.inpaint(image_pil, mask_pil, self.inpainting_method)
            
            # Create output file
            timestamp = int(time.time())
            output_filename = f"inpainted_{timestamp}.png"
            output_path = os.path.join("outputs", output_filename)
            
            # Ensure output directory exists
            os.makedirs("outputs", exist_ok=True)
            
            # Save result
            completed_image.save(output_path, 'PNG')
            self.last_inpainted_file = output_path
            
            status_msg = f"✅ 图片补全完成！使用 {self.inpainting_method} 方法"
            
            return completed_image, status_msg, output_path if os.path.exists(output_path) else None
            
        except Exception as e:
            error_msg = f"图片补全失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return None, error_msg, None
    
    def toggle_processing_mode(self, use_inpainting: bool) -> tuple:
        """Toggle between cutout and inpainting mode"""
        self.use_inpainting = use_inpainting
        
        # Clear selection and update visualization
        self.selected_masks = []
        
        if self.segmentation_image is not None and self.display_image is not None:
            self.segmentation_image = visualize_masks_with_borders(
                self.display_image, self.current_masks, self.selected_masks
            )
            
            mode_name = "补全模式" if use_inpainting else "抠图模式"
            return numpy_to_pil(self.segmentation_image), None, f"已切换到{mode_name}"
        
        mode_name = "补全模式" if use_inpainting else "抠图模式"
        return None, None, f"已切换到{mode_name}"
    
    def export_selected(self) -> tuple:
        """Export selected regions"""
        if self.use_inpainting:
            return self.generate_inpainting()
        else:
            return self._generate_cutout_result()
    
    def change_sam_model(self, new_model_size: str, current_image: Image.Image) -> tuple:
        """Change SAM model size and re-analyze current image"""
        try:
            if new_model_size == self.model_size:
                return None, f"已经是 {new_model_size} 模型"
            
            print(f"🔄 Switching to SAM2 {new_model_size} model...")
            self.model_size = new_model_size
            
            # Reload SAM model
            self.predictor = SAMPredictor(model_size=new_model_size)
            print(f"✅ 已切换到 {new_model_size} 模型")
            
            # Re-analyze current image if available
            if current_image is not None:
                return self.upload_image(current_image)
            else:
                return None, f"已切换到 {new_model_size} 模型，请重新上传图片"
                
        except Exception as e:
            error_msg = f"模型切换失败: {str(e)}"
            print(error_msg)
            return None, error_msg
    
    def clear_selection(self) -> tuple:
        """Clear current selection"""
        self.selected_masks = []
        
        # Update visualization
        if self.segmentation_image is not None and self.display_image is not None:
            self.segmentation_image = visualize_masks_with_borders(
                self.display_image, self.current_masks, self.selected_masks
            )
            return numpy_to_pil(self.segmentation_image), None, "已清空所有选择"
        
        return None, None, "已清空所有选择"
    
    def update_matting_params_realtime(self, use_matting: bool, trimap_size: int, 
                                     matting_quality: str, detail_enhancement: float) -> tuple:
        """实时更新Matting参数"""
        # 更新参数
        self.use_matting = use_matting
        self.trimap_size = trimap_size
        self.matting_quality = matting_quality
        self.detail_enhancement = detail_enhancement
        
        # 如果有选中的mask并且在抠图模式，重新生成抠图
        if self.selected_masks and not self.use_inpainting:
            return self._generate_cutout_result()
        
        return None, "参数已更新", None
    
    def update_selection_mode(self, mode: str) -> tuple:
        """更新选择模式"""
        self.selection_mode = mode
        
        # 清空当前选择
        self.selected_masks = []
        
        # 更新可视化
        if self.segmentation_image is not None and self.display_image is not None:
            self.segmentation_image = visualize_masks_with_borders(
                self.display_image, self.current_masks, self.selected_masks
            )
            return numpy_to_pil(self.segmentation_image), None, f"已切换到{mode}模式"
        
        return None, None, f"已切换到{mode}模式"


def create_interface():
    """Create the complete Gradio interface"""
    
    # Initialize the application
    app = AIImageCutoutComplete(model_size="tiny")
    
    with gr.Blocks(title="AI智能抠图与补全工具 - 完整版", theme=gr.themes.Soft()) as demo:
        gr.Markdown("""
        # 🎨 AI智能抠图与补全工具 - 完整版
        
        **最新功能完整版本** | 使用 SAM2 + Matting + LaMa 技术实现高质量抠图和图片补全
        
        ### ✨ 功能特色：
        - **🔍 智能分割**：SAM2模型自动识别图片中的所有物体
        - **✂️ 高质量抠图**：Matting算法优化边缘细节（头发丝、半透明区域）
        - **🖼️ 图片补全**：LaMa模型智能填补被移除的区域
        - **🎯 多选功能**：支持单选/多选模式
        - **⚙️ 实时调整**：参数实时生效，无需点击更新
        
        ### 🚀 使用说明：
        1. **上传图片** → 系统自动分析并显示可选区域
        2. **选择模式** → 抠图模式：获得透明背景 | 补全模式：智能填补背景
        3. **点击区域** → 选择要处理的物体
        4. **调整参数** → 实时预览效果
        5. **下载结果** → 一键保存高质量PNG
        """)
        
        with gr.Row():
            with gr.Column(scale=1):
                # 图片上传
                input_image = gr.Image(
                    label="📸 上传图片",
                    type="pil",
                    elem_id="input_image"
                )
                
                # 核心设置
                with gr.Group():
                    gr.Markdown("### 🎯 核心设置")
                    
                    # 处理模式选择
                    processing_mode = gr.Checkbox(
                        value=False,
                        label="🖼️ 启用补全模式",
                        info="开启：选中区域将被AI补全背景 | 关闭：传统抠图模式（透明背景）"
                    )
                    
                    # SAM 模型选择
                    model_selector = gr.Dropdown(
                        choices=["tiny", "small", "base_plus", "large"],
                        value="tiny",
                        label="🧠 SAM2 模型",
                        info="tiny(最快) → small → base_plus → large(最准确)"
                    )
                    
                    # 选择模式
                    selection_mode = gr.Radio(
                        choices=["single", "multiple"],
                        value="single",
                        label="🎯 选择模式",
                        info="single：点击直接处理 | multiple：选择多个区域后统一处理"
                    )
                
                # Matting 设置
                with gr.Accordion("🎨 Matting 高级设置（抠图模式）", open=False):
                    use_matting = gr.Checkbox(
                        value=True,
                        label="✨ 启用 Matting 增强",
                        info="使用AI算法优化边缘细节，特别适用于头发丝、半透明区域"
                    )
                    
                    with gr.Row():
                        trimap_size = gr.Slider(
                            minimum=5,
                            maximum=30,
                            step=5,
                            value=10,
                            label="📏 边缘过渡区域",
                            info="控制边缘处理的精细程度"
                        )
                        
                        matting_quality = gr.Radio(
                            choices=["fast", "balanced", "high"],
                            value="balanced",
                            label="⚡ 处理质量",
                            info="质量越高处理越慢"
                        )
                    
                    detail_enhancement = gr.Slider(
                        minimum=0.5,
                        maximum=2.0,
                        step=0.1,
                        value=1.0,
                        label="🔬 细节增强",
                        info="增强头发丝等细节的清晰度"
                    )
                
                # 补全设置
                with gr.Accordion("🖼️ 补全设置", open=False):
                    inpainting_method = gr.Radio(
                        choices=["telea", "ns", "fast_marching"],
                        value="telea",
                        label="🛠️ 补全算法",
                        info="telea: 适合小面积 | ns: 适合线性结构 | fast_marching: 速度最快"
                    )
                    
                    inpainting_preview_alpha = gr.Slider(
                        minimum=0.3,
                        maximum=1.0,
                        step=0.1,
                        value=0.7,
                        label="👁️ 预览透明度",
                        info="控制红色遮罩的透明度"
                    )
                
                # 多选模式按钮
                with gr.Row(visible=True) as multi_select_buttons:
                    clear_btn = gr.Button("🗑️ 清空选择", size="sm")
                    export_btn = gr.Button("🚀 处理选中区域", variant="primary", size="sm")
                
                # 状态信息
                status_text = gr.Textbox(
                    label="📊 状态信息",
                    interactive=False,
                    value="请上传图片开始使用 🚀",
                    lines=2
                )
            
            with gr.Column(scale=1):
                # 分割结果展示
                segmentation_output = gr.Image(
                    label="🎯 点击选择区域",
                    type="pil",
                    interactive=True,
                    elem_id="segmentation_output"
                )
                
                # 处理结果
                result_output = gr.Image(
                    label="🎨 处理结果",
                    type="pil",
                    elem_id="result_output"
                )
                
                # 下载按钮
                download_btn = gr.File(
                    label="💾 下载文件",
                    visible=True,
                    elem_id="download_btn"
                )
        
        # 事件处理
        
        # 上传图片
        input_image.upload(
            fn=app.upload_image,
            inputs=[input_image],
            outputs=[segmentation_output, status_text]
        )
        
        # 图片变化时也触发分析
        input_image.change(
            fn=app.upload_image,
            inputs=[input_image],
            outputs=[segmentation_output, status_text]
        )
        
        # 处理模式切换
        processing_mode.change(
            fn=app.toggle_processing_mode,
            inputs=[processing_mode],
            outputs=[segmentation_output, result_output, status_text]
        )
        
        # SAM 模型切换
        model_selector.change(
            fn=app.change_sam_model,
            inputs=[model_selector, input_image],
            outputs=[segmentation_output, status_text]
        )
        
        # 选择模式切换
        selection_mode.change(
            fn=app.update_selection_mode,
            inputs=[selection_mode],
            outputs=[segmentation_output, result_output, status_text]
        )
        
        # 点击分割图像 - 修复点击事件问题
        def click_wrapper(evt: gr.SelectData):
            try:
                if evt is None:
                    return None, None, "无效的点击事件", None
                result = app.on_image_click(evt)
                if len(result) == 4:
                    return result[0], result[1], result[2], result[3]
                else:
                    return None, None, "点击处理失败", None
            except Exception as e:
                print(f"点击事件处理错误: {e}")
                return None, None, f"点击处理失败: {str(e)}", None
        
        segmentation_output.select(
            fn=click_wrapper,
            inputs=[],
            outputs=[segmentation_output, result_output, status_text, download_btn]
        )
        
        # 清空选择
        clear_btn.click(
            fn=app.clear_selection,
            inputs=[],
            outputs=[segmentation_output, result_output, status_text]
        )
        
        # 导出选中区域 - 修复返回值数量和文件路径问题
        def export_wrapper():
            try:
                result = app.export_selected()
                if len(result) == 3:  # inpainting模式返回3个值
                    # 确保第三个值是有效的文件路径
                    image, status, file_path = result
                    if file_path and os.path.exists(file_path):
                        return image, status, file_path
                    else:
                        return image, status, None
                elif len(result) == 4:  # cutout模式返回4个值
                    # 跳过segmentation_image，确保第四个值是有效的文件路径
                    _, image, status, file_path = result
                    if file_path and os.path.exists(file_path):
                        return image, status, file_path
                    else:
                        return image, status, None
                else:
                    return None, "处理失败", None
            except Exception as e:
                print(f"导出错误: {e}")
                return None, f"导出失败: {str(e)}", None
        
        export_btn.click(
            fn=export_wrapper,
            inputs=[],
            outputs=[result_output, status_text, download_btn]
        )
        
        # 实时参数调整 - 修复返回值数量和文件路径问题
        def update_matting_wrapper(use_matting, trimap_size, matting_quality, detail_enhancement):
            try:
                result = app.update_matting_params_realtime(use_matting, trimap_size, matting_quality, detail_enhancement)
                if len(result) == 4 and result[0] is not None:  # 有图片返回
                    _, image, status, file_path = result
                    if file_path and os.path.exists(file_path):
                        return image, status, file_path
                    else:
                        return image, status, None
                elif len(result) == 3:
                    return result[0], result[1], result[2]
                else:
                    return None, "参数已更新", None
            except Exception as e:
                print(f"参数更新错误: {e}")
                return None, f"参数更新失败: {str(e)}", None
        
        for component in [use_matting, trimap_size, matting_quality, detail_enhancement]:
            component.change(
                fn=update_matting_wrapper,
                inputs=[use_matting, trimap_size, matting_quality, detail_enhancement],
                outputs=[result_output, status_text, download_btn]
            )
        
        # 预览透明度调整
        inpainting_preview_alpha.change(
            fn=lambda alpha: setattr(app, 'inpainting_preview_alpha', alpha),
            inputs=[inpainting_preview_alpha],
            outputs=[]
        )
        
        # 补全方法调整
        inpainting_method.change(
            fn=lambda method: setattr(app, 'inpainting_method', method),
            inputs=[inpainting_method],
            outputs=[]
        )
        
        # 根据选择模式显示/隐藏按钮
        def update_ui_visibility(mode):
            return gr.update(visible=(mode == "multiple"))
        
        selection_mode.change(
            fn=update_ui_visibility,
            inputs=[selection_mode],
            outputs=[multi_select_buttons]
        )
    
    return demo


def main():
    """Main function to run the application"""
    print("=" * 60)
    print("🚀 启动 AI智能抠图与补全工具 - 完整版")
    print("=" * 60)
    
    # Create and launch the interface
    demo = create_interface()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,  # 使用主端口
        share=False,
        show_error=True,
        quiet=False,
        inbrowser=True  # 自动打开浏览器
    )


if __name__ == "__main__":
    main() 