import gradio as gr
import numpy as np
from PIL import Image
import traceback
import os
import cv2
import time
import torch

# Import our custom modules
from sam_model import SAMPredictor
from matting_model import MODNetWrapper, create_rgba_with_matte
from utils import (
    pil_to_numpy, numpy_to_pil, visualize_masks, visualize_masks_with_borders,
    find_mask_at_point, resize_image_for_display, merge_masks,
    apply_mask_to_image, create_cutout_image
)

class AIImageCutoutGPU:
    def __init__(self, model_size: str = "tiny", use_gpu: bool = True):
        """
        Initialize the AI Image Cutout application with GPU support
        """
        self.model_size = model_size
        self.use_gpu = use_gpu
        self.predictor = None
        self.matting_model = None
        self.current_image = None  # 原始图像
        self.display_image = None  # 调整后的显示图像
        self.current_masks = []
        self.selected_masks = []  # 存储选中的区域
        self.segmentation_image = None
        self.last_cutout_file = None
        self.selection_mode = "single"  # "single" or "multiple"
        
        # Matting 参数
        self.use_matting = True
        self.trimap_size = 10
        self.matting_quality = "balanced"
        self.detail_enhancement = 1.0
        
        # GPU状态信息
        self.device_info = self._get_device_info()
        
        # Initialize models
        self._init_models()
    
    def _get_device_info(self):
        """获取设备信息"""
        if torch.cuda.is_available():
            device_name = torch.cuda.get_device_name(0)
            memory_total = torch.cuda.get_device_properties(0).total_memory / 1024**3
            return {
                "device": "cuda",
                "device_name": device_name,
                "memory_total_gb": f"{memory_total:.1f}",
                "cuda_available": True
            }
        else:
            return {
                "device": "cpu",
                "device_name": "CPU",
                "memory_total_gb": "N/A",
                "cuda_available": False
            }
    
    def _init_models(self):
        """Initialize models with GPU support"""
        try:
            # Initialize SAM model
            print(f"Loading SAM2 {self.model_size} model...")
            self.predictor = SAMPredictor(model_size=self.model_size, use_gpu=self.use_gpu)
            print("SAM model loaded successfully!")
            
            # Initialize Matting model
            print("Loading Matting model...")
            # 指定官方MODNet权重路径
            modnet_path = "checkpoints/modnet_photographic_portrait_matting.pth"
            self.matting_model = MODNetWrapper(use_gpu=self.use_gpu, ckpt_path=modnet_path)
            
            # 获取模型状态
            model_info = self.matting_model.get_model_info()
            if model_info["use_real_model"]:
                print(f"✅ MODNet AI model loaded successfully! ({model_info['model_type']})")
            else:
                print(f"⚠️ Using fallback matting method: {model_info['model_type']}")
            
            return True
            
        except Exception as e:
            print(f"Model initialization error: {e}")
            traceback.print_exc()
            return False
    
    def get_device_status(self):
        """获取设备状态信息"""
        status = f"🖥️ 设备: {self.device_info['device_name']}"
        if self.device_info['cuda_available']:
            status += f" ({self.device_info['memory_total_gb']}GB)"
        
        # 添加模型信息
        if self.predictor:
            sam_info = self.predictor.get_model_info()
            status += f"\n🤖 SAM: {sam_info.get('device', 'unknown')}"
        
        if self.matting_model:
            matting_info = self.matting_model.get_model_info()
            status += f"\n🎨 Matting: {matting_info.get('model_type', 'unknown')}"
        
        return status
    
    def upload_image(self, image: Image.Image) -> tuple:
        """Handle image upload and run SAM prediction"""
        if image is None:
            return None, "请上传图片"
        
        try:
            # Convert and store image
            self.current_image = pil_to_numpy(image)
            print(f"📸 Image set successfully. Shape: {self.current_image.shape}")
            
            # Resize for display if needed
            self.display_image = resize_image_for_display(self.current_image)
            
            # Clear previous results
            self.current_masks = []
            self.selected_masks = []
            
            # Run SAM prediction
            print("🔍 Generating masks...")
            if self.predictor and self.display_image is not None:
                # Set image for prediction
                self.predictor.set_image(self.display_image)
                
                # Generate masks using predict_everything
                self.current_masks = self.predictor.predict_everything()
                print(f"✅ Generated {len(self.current_masks)} masks")
                
                # Visualize masks
                if self.current_masks:
                    self.segmentation_image = visualize_masks_with_borders(
                        self.display_image, self.current_masks, self.selected_masks
                    )
                    return self.segmentation_image, f"✅ 生成了 {len(self.current_masks)} 个区域，点击选择要抠图的区域"
                else:
                    return None, "❌ 未能生成任何区域，请尝试其他图片"
            else:
                return None, "❌ 模型未正确加载"
                
        except Exception as e:
            print(f"❌ 图片处理失败: {e}")
            traceback.print_exc()
            return None, f"❌ 图片处理失败: {e}"
    
    def on_image_click(self, evt: gr.SelectData) -> tuple:
        """Handle clicks on the segmentation image"""
        if not self.current_masks:
            return None, None, "请先上传图片并生成区域"
        
        try:
            x, y = evt.index[0], evt.index[1]
            print(f"Click at: ({x}, {y})")
            
            # Find the mask at the clicked point
            clicked_mask = find_mask_at_point(self.current_masks, (x, y))
            
            if clicked_mask is None:
                return None, None, "点击位置没有找到区域"
            
            if self.selection_mode == "single":
                return self._process_single_selection(clicked_mask)
            else:
                return self._process_multiple_selection(clicked_mask)
                
        except Exception as e:
            print(f"❌ 点击处理失败: {e}")
            traceback.print_exc()
            return None, None, f"❌ 点击处理失败: {e}"
    
    def _process_single_selection(self, clicked_mask):
        """处理单选模式"""
        # Clear previous selections
        self.selected_masks = [clicked_mask]
        
        # Update visualization
        self.segmentation_image = visualize_masks_with_borders(
            self.display_image, self.current_masks, self.selected_masks
        )
        
        # Generate cutout
        cutout_result, status = self._generate_cutout_result()
        
        return self.segmentation_image, cutout_result, status
    
    def _process_multiple_selection(self, clicked_mask):
        """处理多选模式"""
        # Check if mask is already selected
        is_already_selected = any(
            np.array_equal(selected_mask['segmentation'], clicked_mask['segmentation'])
            for selected_mask in self.selected_masks
        )
        
        if is_already_selected:
            # Remove from selection
            self.selected_masks = [
                mask for mask in self.selected_masks
                if not np.array_equal(mask['segmentation'], clicked_mask['segmentation'])
            ]
            status = f"移除区域，当前选中 {len(self.selected_masks)} 个区域"
        else:
            # Add to selection
            self.selected_masks.append(clicked_mask)
            status = f"添加区域，当前选中 {len(self.selected_masks)} 个区域"
        
        # Update visualization
        self.segmentation_image = visualize_masks_with_borders(
            self.display_image, self.current_masks, self.selected_masks
        )
        
        return self.segmentation_image, None, status
    
    def _generate_cutout_result(self) -> tuple:
        """Generate cutout result"""
        try:
            if not self.selected_masks:
                return None, "请先选择要抠图的区域"
            
            print("Using Matting for high-quality edges...")
            
            # Merge selected masks
            merged_mask = merge_masks([mask['segmentation'] for mask in self.selected_masks])
            
            # Apply matting if enabled
            if self.use_matting and self.matting_model:
                try:
                    # Use matting model for high-quality edges
                    matte = self.matting_model.predict(self.current_image, merged_mask)
                    
                    # Create RGBA image with matte
                    cutout_image = create_rgba_with_matte(self.current_image, matte)
                    
                    # Save result
                    output_path = f"cutout_result_{int(time.time())}.png"
                    cutout_image.save(output_path)
                    self.last_cutout_file = output_path
                    
                    return output_path, "✅ 抠图完成！ | 使用 Matting 增强"
                    
                except Exception as e:
                    print(f"Matting failed: {e}, falling back to basic method")
                    # Fallback to basic method
                    pass
            
            # Basic cutout method
            cutout_image = create_cutout_image(self.current_image, merged_mask)
            
            # Save result
            output_path = f"cutout_result_{int(time.time())}.png"
            cutout_image.save(output_path)
            self.last_cutout_file = output_path
            
            return output_path, "✅ 抠图完成！"
            
        except Exception as e:
            print(f"❌ 抠图生成失败: {e}")
            traceback.print_exc()
            return None, f"❌ 抠图生成失败: {e}"
    
    def export_selected(self) -> tuple:
        """Export the selected cutout"""
        return self._generate_cutout_result()
    
    def clear_selection(self) -> tuple:
        """Clear all selections"""
        self.selected_masks = []
        if self.current_masks:
            self.segmentation_image = visualize_masks_with_borders(
                self.display_image, self.current_masks, self.selected_masks
            )
            return self.segmentation_image, "已清除所有选择"
        return None, "没有可清除的选择"
    
    def change_sam_model(self, new_model_size: str, current_image: Image.Image) -> tuple:
        """Change SAM model size"""
        try:
            print(f"🔄 Switching to SAM2 {new_model_size} model...")
            
            # Update model size
            self.model_size = new_model_size
            
            # Reinitialize predictor with new model
            self.predictor = SAMPredictor(model_size=self.model_size, use_gpu=self.use_gpu)
            
            # Re-analyze current image if available
            if current_image is not None:
                return self.upload_image(current_image)
            else:
                return None, f"✅ 已切换到 {new_model_size} 模型"
                
        except Exception as e:
            print(f"❌ 模型切换失败: {e}")
            return None, f"❌ 模型切换失败: {e}"
    
    def update_matting_params_realtime(self, use_matting: bool, trimap_size: int, 
                                     matting_quality: str, detail_enhancement: float) -> tuple:
        """Update matting parameters in real-time"""
        self.use_matting = use_matting
        self.trimap_size = trimap_size
        self.matting_quality = matting_quality
        self.detail_enhancement = detail_enhancement
        
        # If we have a current selection, regenerate the result
        if self.selected_masks and self.selection_mode == "single":
            cutout_result, status = self._generate_cutout_result()
            return cutout_result, status
        
        return None, "参数已更新"
    
    def update_selection_mode(self, mode: str) -> tuple:
        """Update selection mode"""
        self.selection_mode = mode
        self.selected_masks = []  # Clear selections when changing mode
        
        if self.current_masks:
            self.segmentation_image = visualize_masks_with_borders(
                self.display_image, self.current_masks, self.selected_masks
            )
            return self.segmentation_image, f"已切换到{mode}模式"
        
        return None, f"已切换到{mode}模式"

def create_interface():
    """Create the Gradio interface"""
    app = AIImageCutoutGPU(use_gpu=True)  # 启用GPU加速
    
    with gr.Blocks(title="AI智能抠图工具 - GPU加速版", theme=gr.themes.Soft()) as interface:
        gr.Markdown("# 🎨 AI智能抠图工具 - GPU加速版")
        gr.Markdown("### 上传图片，自动识别区域，点击选择要抠图的元素")
        
        # Device status
        device_status = gr.Textbox(
            value=app.get_device_status(),
            label="🖥️ 设备状态",
            interactive=False
        )
        
        with gr.Row():
            with gr.Column(scale=2):
                # Image upload
                input_image = gr.Image(
                    label="📸 上传图片",
                    type="pil",
                    height=400
                )
                
                # SAM model selection
                sam_model_dropdown = gr.Dropdown(
                    choices=["tiny", "small", "base_plus", "large"],
                    value="tiny",
                    label="🤖 SAM模型大小",
                    info="选择模型大小，越大越精确但越慢"
                )
                
                # Processing mode
                processing_mode = gr.Radio(
                    choices=["抠图", "补全"],
                    value="抠图",
                    label="🎯 处理模式",
                    info="抠图：提取选中区域 | 补全：填充未选中区域"
                )
                
                # Selection mode
                selection_mode = gr.Radio(
                    choices=["单选", "多选"],
                    value="单选",
                    label="👆 选择模式",
                    info="单选：一次选择一个区域 | 多选：可同时选择多个区域"
                )
            
            with gr.Column(scale=2):
                # Segmentation result
                segmentation_output = gr.Image(
                    label="🎯 区域识别结果",
                    height=400,
                    interactive=True
                )
                
                # Cutout result
                cutout_output = gr.File(
                    label="✂️ 抠图结果",
                    file_count="single"
                )
                
                # Status
                status_text = gr.Textbox(
                    label="📊 状态信息",
                    interactive=False
                )
        
        # Matting settings (accordion)
        with gr.Accordion("🎨 Matting设置", open=False):
            use_matting = gr.Checkbox(
                value=True,
                label="启用Matting增强",
                info="使用AI模型进行边缘优化"
            )
            
            trimap_size = gr.Slider(
                minimum=5,
                maximum=20,
                value=10,
                step=1,
                label="Trimap大小",
                info="边缘过渡区域大小"
            )
            
            matting_quality = gr.Dropdown(
                choices=["fast", "balanced", "high"],
                value="balanced",
                label="质量设置",
                info="处理质量 vs 速度平衡"
            )
            
            detail_enhancement = gr.Slider(
                minimum=0.5,
                maximum=2.0,
                value=1.0,
                step=0.1,
                label="细节增强",
                info="边缘细节增强强度"
            )
        
        # Control buttons
        with gr.Row():
            export_btn = gr.Button("💾 导出抠图", variant="primary")
            clear_btn = gr.Button("🗑️ 清除选择")
        
        # Event handlers
        input_image.change(
            fn=app.upload_image,
            inputs=[input_image],
            outputs=[segmentation_output, status_text]
        )
        
        segmentation_output.select(
            fn=app.on_image_click,
            inputs=[segmentation_output],
            outputs=[segmentation_output, cutout_output, status_text]
        )
        
        sam_model_dropdown.change(
            fn=app.change_sam_model,
            inputs=[sam_model_dropdown, input_image],
            outputs=[segmentation_output, status_text]
        )
        
        selection_mode.change(
            fn=app.update_selection_mode,
            inputs=[selection_mode],
            outputs=[segmentation_output, status_text]
        )
        
        # Real-time parameter updates
        use_matting.change(
            fn=app.update_matting_params_realtime,
            inputs=[use_matting, trimap_size, matting_quality, detail_enhancement],
            outputs=[cutout_output, status_text]
        )
        
        trimap_size.change(
            fn=app.update_matting_params_realtime,
            inputs=[use_matting, trimap_size, matting_quality, detail_enhancement],
            outputs=[cutout_output, status_text]
        )
        
        matting_quality.change(
            fn=app.update_matting_params_realtime,
            inputs=[use_matting, trimap_size, matting_quality, detail_enhancement],
            outputs=[cutout_output, status_text]
        )
        
        detail_enhancement.change(
            fn=app.update_matting_params_realtime,
            inputs=[use_matting, trimap_size, matting_quality, detail_enhancement],
            outputs=[cutout_output, status_text]
        )
        
        export_btn.click(
            fn=app.export_selected,
            inputs=[],
            outputs=[cutout_output, status_text]
        )
        
        clear_btn.click(
            fn=app.clear_selection,
            inputs=[],
            outputs=[segmentation_output, status_text]
        )
    
    return interface

def main():
    """Main function"""
    print("=" * 60)
    print("🚀 启动 AI智能抠图工具 - GPU加速版")
    print("=" * 60)
    
    # Create and launch interface
    interface = create_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )

if __name__ == "__main__":
    main() 