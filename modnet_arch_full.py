"""
Official MODNet Architecture Implementation
从官方MODNet代码库复制的完整架构
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import os
import sys

# 将MODNet目录添加到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'MODNet', 'src'))

try:
    from models.modnet import MODNet as OriginalMODNet
    from models.backbones import SUPPORTED_BACKBONES
    MODNET_AVAILABLE = True
    print("✅ 成功导入官方MODNet架构")
except ImportError as e:
    print(f"⚠️ 无法导入官方MODNet架构: {e}")
    MODNET_AVAILABLE = False

class MODNet(nn.Module):
    """MODNet包装器 - 使用官方架构"""
    
    def __init__(self, hr_channels=32, backbone_arch='mobilenetv2'):
        super(MODNet, self).__init__()
        
        if MODNET_AVAILABLE:
            self.model = OriginalMODNet(
                in_channels=3, 
                hr_channels=hr_channels, 
                backbone_arch=backbone_arch, 
                backbone_pretrained=False  # 我们手动加载权重
            )
        else:
            raise ImportError("官方MODNet架构不可用")
    
    def forward(self, img, inference=False):
        if inference:
            pred_semantic, pred_detail, pred_matte = self.model(img, inference=False)
            return pred_matte
        else:
            return self.model(img, inference=False)

def load_modnet_model(ckpt_path=None, device='cpu'):
    """加载MODNet模型"""
    try:
        model = MODNet(hr_channels=32)
        
        if ckpt_path and os.path.exists(ckpt_path):
            print(f"🔄 Loading MODNet checkpoint from {ckpt_path}")
            checkpoint = torch.load(ckpt_path, map_location=device)
            
            # 处理不同的checkpoint格式
            if 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            elif 'model' in checkpoint:
                state_dict = checkpoint['model']
            else:
                state_dict = checkpoint
            
            # 处理module.前缀（DataParallel保存的模型）
            new_state_dict = {}
            for k, v in state_dict.items():
                if k.startswith('module.'):
                    name = k[7:]  # 去掉'module.'前缀
                else:
                    name = k
                new_state_dict[name] = v
            
            # 尝试加载权重
            try:
                model.model.load_state_dict(new_state_dict, strict=True)
                print("✅ Loaded MODNet checkpoint successfully (strict mode)")
            except Exception as e:
                print(f"⚠️ Strict loading failed: {e}")
                # 使用非严格模式
                missing_keys, unexpected_keys = model.model.load_state_dict(new_state_dict, strict=False)
                print(f"⚠️ Missing keys: {len(missing_keys)}")
                print(f"⚠️ Unexpected keys: {len(unexpected_keys)}")
                print("✅ Loaded MODNet checkpoint successfully (partial)")
        else:
            print("⚠️ No checkpoint provided, using randomly initialized weights")
        
        model.to(device)
        model.eval()
        return model
        
    except Exception as e:
        print(f"❌ Failed to load MODNet model: {e}")
        raise

def test_modnet():
    """测试MODNet模型加载和推理"""
    import cv2
    import numpy as np
    from PIL import Image
    import time
    
    print("🧪 测试官方MODNet架构...")
    
    if not MODNET_AVAILABLE:
        print("❌ 官方MODNet架构不可用，跳过测试")
        return
    
    # 创建测试图像
    test_img = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
    img_tensor = torch.from_numpy(test_img).float().permute(2, 0, 1).unsqueeze(0) / 255.0
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    img_tensor = img_tensor.to(device)
    
    # 测试不同权重文件
    weight_paths = [
        "checkpoints/modnet_photographic_portrait_matting.pth",
        "checkpoints/modnet_webcam_portrait_matting.ckpt",
    ]
    
    for weight_path in weight_paths:
        if os.path.exists(weight_path):
            try:
                print(f"\n测试权重文件: {weight_path}")
                start_time = time.time()
                
                # 加载模型
                model = load_modnet_model(weight_path, device)
                load_time = time.time() - start_time
                print(f"模型加载时间: {load_time:.2f}秒")
                
                # 推理
                start_time = time.time()
                with torch.no_grad():
                    pred = model(img_tensor, inference=True)
                inference_time = time.time() - start_time
                print(f"推理时间: {inference_time:.2f}秒")
                
                # 转换结果
                matte = pred[0, 0].cpu().numpy()
                matte_uint8 = (matte * 255).astype(np.uint8)
                
                # 保存结果
                output_dir = "test_outputs"
                os.makedirs(output_dir, exist_ok=True)
                output_name = os.path.basename(weight_path).replace('.pth', '').replace('.ckpt', '')
                cv2.imwrite(f"{output_dir}/{output_name}_matte_official.png", matte_uint8)
                
                print(f"✅ 测试成功! 结果保存到 {output_dir}/{output_name}_matte_official.png")
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
                import traceback
                traceback.print_exc()
        else:
            print(f"⚠️ 权重文件不存在: {weight_path}")

if __name__ == "__main__":
    test_modnet() 