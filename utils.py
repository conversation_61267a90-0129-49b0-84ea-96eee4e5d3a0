import numpy as np
import cv2
from PIL import Image, ImageFilter
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import random
from typing import List, Tuple, Optional
import io
import base64
from scipy import ndimage
from skimage import filters, morphology

def load_image(image_path: str) -> np.ndarray:
    """
    Load image from file path
    
    Args:
        image_path: Path to image file
        
    Returns:
        Image as numpy array in RGB format
    """
    image = Image.open(image_path)
    if image.mode != 'RGB':
        image = image.convert('RGB')
    return np.array(image)

def pil_to_numpy(pil_image: Image.Image) -> np.ndarray:
    """Convert PIL Image to numpy array"""
    if pil_image.mode != 'RGB':
        pil_image = pil_image.convert('RGB')
    return np.array(pil_image)

def numpy_to_pil(np_image: np.ndarray) -> Image.Image:
    """Convert numpy array to PIL Image"""
    if np_image.dtype != np.uint8:
        np_image = (np_image * 255).astype(np.uint8)
    return Image.fromarray(np_image)

def generate_random_colors(num_colors: int) -> List[Tuple[int, int, int]]:
    """
    Generate random colors for mask visualization
    
    Args:
        num_colors: Number of colors to generate
        
    Returns:
        List of RGB color tuples
    """
    colors = []
    for _ in range(num_colors):
        # Generate bright, saturated colors
        hue = random.random()
        saturation = 0.7 + random.random() * 0.3  # 0.7-1.0
        value = 0.8 + random.random() * 0.2       # 0.8-1.0
        
        rgb = mcolors.hsv_to_rgb([hue, saturation, value])
        colors.append(tuple(int(c * 255) for c in rgb))
    
    return colors

def visualize_masks(image: np.ndarray, masks: List[dict], selected_masks: Optional[List[dict]] = None, alpha: float = 0.5) -> np.ndarray:
    """
    在原始图像上可视化所有遮罩，自动处理尺寸不匹配问题
    
    Args:
        image: 原始图像 (H, W, 3)
        masks: SAM生成的遮罩字典列表
        selected_masks: 选中的遮罩字典列表
        alpha: 遮罩透明度
        
    Returns:
        带有彩色遮罩的图像
    """
    if not masks:
        return image.copy()
    
    # 获取目标尺寸
    h, w = image.shape[:2]
    result = image.copy()
    
    # 创建遮罩层
    mask_overlay = np.zeros((h, w, 3), dtype=np.uint8)
    
    # 生成随机颜色
    colors = generate_random_colors(len(masks))
    
    # 处理所有遮罩
    for i, mask_dict in enumerate(masks):
        try:
            mask = mask_dict['segmentation']
            
            # 确保mask是二维的
            if len(mask.shape) > 2:
                mask = mask[:, :, 0]
            
            # 调整遮罩尺寸
            if mask.shape[:2] != (h, w):
                # print(f"Resizing mask from {mask.shape} to ({h}, {w})")
                mask = cv2.resize(mask.astype(np.float32), (w, h), interpolation=cv2.INTER_LINEAR) > 0.5
                mask_dict['segmentation'] = mask  # 更新字典中的mask
            
            # 确保mask是布尔类型
            mask = mask.astype(bool)
            
            # 为选中的遮罩使用特殊颜色
            if selected_masks and any(id(m) == id(mask_dict) for m in selected_masks):
                color = [255, 50, 50]  # 红色
                local_alpha = min(0.8, alpha + 0.2)  # 增加不透明度
            else:
                color = colors[i]
                local_alpha = alpha
            
            # 应用颜色到遮罩层
            mask_overlay[mask] = color
            
        except Exception as e:
            print(f"Warning: Failed to process mask {i}: {e}")
            continue
    
    # 合并原图和遮罩层
    result = cv2.addWeighted(result, 1, mask_overlay, alpha, 0)
    
    return result

def visualize_masks_with_borders(image: np.ndarray, masks: List[dict], selected_masks: Optional[List[dict]] = None, alpha: float = 0.6) -> np.ndarray:
    """
    可视化遮罩并添加边界
    
    Args:
        image: 原始图像
        masks: 遮罩列表
        selected_masks: 选中的遮罩列表
        alpha: 透明度
    
    Returns:
        带有遮罩和边界的图像
    """
    # 首先绘制基础遮罩
    result = visualize_masks(image, masks, selected_masks, alpha)
    
    # 获取图像尺寸
    h, w = image.shape[:2]
    
    # 为选中的遮罩添加边界
    if selected_masks:
        for mask_dict in selected_masks:
            mask = mask_dict['segmentation']
            
            # 确保mask尺寸正确
            if mask.shape[:2] != (h, w):
                mask = cv2.resize(mask.astype(float), (w, h), interpolation=cv2.INTER_LINEAR) > 0.5
            
            # 找到轮廓
            mask_uint8 = mask.astype(np.uint8) * 255
            contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 绘制边界
            cv2.drawContours(result, contours, -1, (255, 255, 0), thickness=2)
    
    return result

def apply_mask_to_image(image: np.ndarray, mask: np.ndarray) -> np.ndarray:
    """
    Apply mask to image to create transparent background
    
    Args:
        image: Original image (H, W, 3)
        mask: Boolean mask (H, W)
        
    Returns:
        RGBA image with transparent background
    """
    # Convert to RGBA
    if image.shape[2] == 3:
        rgba_image = np.zeros((image.shape[0], image.shape[1], 4), dtype=np.uint8)
        rgba_image[:, :, :3] = image
        rgba_image[:, :, 3] = 255  # Full opacity
    else:
        rgba_image = image.copy()
    
    # Apply mask to alpha channel
    rgba_image[:, :, 3] = mask.astype(np.uint8) * 255
    
    return rgba_image

def merge_masks(masks: List[np.ndarray]) -> np.ndarray:
    """
    Merge multiple masks into one
    
    Args:
        masks: List of boolean masks
        
    Returns:
        Combined boolean mask
    """
    if not masks:
        return np.array([])
    
    # Start with the first mask
    combined_mask = masks[0].copy()
    
    # Add all other masks
    for mask in masks[1:]:
        combined_mask = np.logical_or(combined_mask, mask)
    
    return combined_mask

def refine_mask_edges(mask: np.ndarray, kernel_size: int = 3, smooth_strength: int = 2) -> np.ndarray:
    """
    增强版边缘细化函数，使用多种技术平滑边缘
    
    Args:
        mask: Binary mask
        kernel_size: Size of morphological kernel
        smooth_strength: 平滑强度 (1-5, 数值越大越平滑)
        
    Returns:
        Refined mask
    """
    # Convert to uint8 if needed
    if mask.dtype == bool:
        mask = mask.astype(np.uint8) * 255
    
    # 1. 基础形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
    
    # Close small holes
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
    
    # Open to smooth edges
    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
    
    # 2. 根据平滑强度应用不同的技术
    if smooth_strength >= 2:
        # 使用更大的kernel进行额外平滑
        larger_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size + 2, kernel_size + 2))
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, larger_kernel)
    
    if smooth_strength >= 3:
        # 高斯模糊平滑
        mask = cv2.GaussianBlur(mask, (5, 5), 1.0)
        # 重新二值化
        _, mask = cv2.threshold(mask, 127, 255, cv2.THRESH_BINARY)
    
    if smooth_strength >= 4:
        # 中值滤波去除噪声
        mask = cv2.medianBlur(mask, 5)
    
    if smooth_strength >= 5:
        # 双边滤波保边平滑
        mask = cv2.bilateralFilter(mask, 9, 75, 75)
        _, mask = cv2.threshold(mask, 127, 255, cv2.THRESH_BINARY)
    
    return mask > 0

def create_soft_edge_mask(mask: np.ndarray, feather_radius: int = 5) -> np.ndarray:
    """
    创建软边缘mask，实现边缘羽化效果
    
    Args:
        mask: Binary mask
        feather_radius: 羽化半径
        
    Returns:
        Soft edge mask with values 0-255
    """
    # 确保mask是uint8格式
    if mask.dtype == bool:
        mask = mask.astype(np.uint8) * 255
    
    # 计算距离变换
    dist_transform = cv2.distanceTransform(mask, cv2.DIST_L2, 5)
    
    # 创建羽化效果
    # 内部区域保持不变，边缘区域逐渐透明
    soft_mask = np.zeros_like(mask, dtype=np.float32)
    
    # 设置羽化区域
    soft_mask[dist_transform > feather_radius] = 255
    
    # 羽化过渡区域
    transition_area = (dist_transform > 0) & (dist_transform <= feather_radius)
    soft_mask[transition_area] = (dist_transform[transition_area] / feather_radius) * 255
    
    return soft_mask.astype(np.uint8)

def apply_anti_aliasing(mask: np.ndarray, scale_factor: int = 4) -> np.ndarray:
    """
    应用抗锯齿处理
    
    Args:
        mask: Binary mask
        scale_factor: 超采样倍数
        
    Returns:
        Anti-aliased mask
    """
    # 上采样
    h, w = mask.shape
    upsampled = cv2.resize(mask.astype(np.uint8), 
                          (w * scale_factor, h * scale_factor), 
                          interpolation=cv2.INTER_CUBIC)
    
    # 高斯模糊
    upsampled = cv2.GaussianBlur(upsampled, (3, 3), 0.5)
    
    # 下采样回原始尺寸
    downsampled = cv2.resize(upsampled, (w, h), interpolation=cv2.INTER_AREA)
    
    return downsampled

def enhance_mask_edges(mask: np.ndarray, 
                      method: str = 'comprehensive',
                      smooth_strength: int = 3,
                      feather_radius: int = 3,
                      anti_alias: bool = True) -> np.ndarray:
    """
    综合边缘增强函数
    
    Args:
        mask: Binary mask
        method: 增强方法 ('basic', 'soft', 'comprehensive')
        smooth_strength: 平滑强度 (1-5)
        feather_radius: 羽化半径
        anti_alias: 是否应用抗锯齿
        
    Returns:
        Enhanced mask
    """
    if method == 'basic':
        # 基础形态学操作
        return refine_mask_edges(mask, smooth_strength=smooth_strength)
    
    elif method == 'soft':
        # 软边缘处理
        refined_mask = refine_mask_edges(mask, smooth_strength=smooth_strength)
        return create_soft_edge_mask(refined_mask, feather_radius)
    
    elif method == 'comprehensive':
        # 综合处理
        # 1. 基础形态学操作
        refined_mask = refine_mask_edges(mask, smooth_strength=smooth_strength)
        
        # 2. 抗锯齿处理
        if anti_alias:
            refined_mask = apply_anti_aliasing(refined_mask)
        
        # 3. 创建软边缘
        soft_mask = create_soft_edge_mask(refined_mask, feather_radius)
        
        return soft_mask
    
    else:
        return mask

def create_cutout_image(image: np.ndarray, mask: np.ndarray, 
                       refine_edges: bool = True,
                       edge_method: str = 'comprehensive',
                       smooth_strength: int = 3,
                       feather_radius: int = 3) -> Image.Image:
    """
    创建具有透明背景的抠图，使用增强的边缘处理
    
    Args:
        image: Original image
        mask: Binary mask
        refine_edges: Whether to refine mask edges
        edge_method: 边缘增强方法 ('basic', 'soft', 'comprehensive')
        smooth_strength: 平滑强度 (1-5)
        feather_radius: 羽化半径
        
    Returns:
        PIL Image with transparent background
    """
    # 增强边缘处理
    if refine_edges:
        mask = enhance_mask_edges(mask, 
                                 method=edge_method,
                                 smooth_strength=smooth_strength,
                                 feather_radius=feather_radius)
    
    # 创建RGBA图像
    if image.shape[2] == 3:
        rgba_image = np.zeros((image.shape[0], image.shape[1], 4), dtype=np.uint8)
        rgba_image[:, :, :3] = image
    else:
        rgba_image = image.copy()
    
    # 应用增强的mask到alpha通道
    if mask.dtype == bool:
        rgba_image[:, :, 3] = mask.astype(np.uint8) * 255
    else:
        rgba_image[:, :, 3] = mask
    
    return Image.fromarray(rgba_image, 'RGBA')

def create_multi_cutout_image(image: np.ndarray, masks: List[np.ndarray], 
                             refine_edges: bool = True,
                             edge_method: str = 'comprehensive',
                             smooth_strength: int = 3,
                             feather_radius: int = 3) -> Image.Image:
    """
    从多个mask创建抠图，使用增强的边缘处理
    
    Args:
        image: Original image
        masks: List of binary masks
        refine_edges: Whether to refine mask edges
        edge_method: 边缘增强方法
        smooth_strength: 平滑强度
        feather_radius: 羽化半径
        
    Returns:
        PIL Image with transparent background
    """
    # Merge all masks
    combined_mask = merge_masks(masks)
    
    # Create cutout image with enhanced edge processing
    return create_cutout_image(image, combined_mask, 
                              refine_edges=refine_edges,
                              edge_method=edge_method,
                              smooth_strength=smooth_strength,
                              feather_radius=feather_radius)

def get_click_coordinates_from_event(event_data) -> Optional[Tuple[int, int]]:
    """
    Extract click coordinates from Gradio event data
    
    Args:
        event_data: Gradio click event data
        
    Returns:
        (x, y) coordinates or None if invalid
    """
    if event_data is None:
        return None
    
    try:
        # Gradio image click returns coordinates in the format [x, y]
        if isinstance(event_data, dict) and 'point' in event_data:
            point = event_data['point']
            return (int(point[0]), int(point[1]))
        elif isinstance(event_data, list) and len(event_data) >= 2:
            return (int(event_data[0]), int(event_data[1]))
        else:
            return None
    except (ValueError, TypeError, KeyError):
        return None

def resize_image_for_display(image: np.ndarray, max_size: int = 800) -> np.ndarray:
    """
    Resize image for display while maintaining aspect ratio
    
    Args:
        image: Input image
        max_size: Maximum size for the longer edge
        
    Returns:
        Resized image
    """
    h, w = image.shape[:2]
    
    # Calculate scaling factor
    scale = min(max_size / w, max_size / h)
    
    if scale < 1:
        new_w = int(w * scale)
        new_h = int(h * scale)
        image = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)
    
    return image

def save_image_to_bytes(image: Image.Image, format: str = 'PNG') -> bytes:
    """
    Save PIL Image to bytes
    
    Args:
        image: PIL Image
        format: Image format ('PNG', 'JPEG', etc.)
        
    Returns:
        Image bytes
    """
    img_bytes = io.BytesIO()
    image.save(img_bytes, format=format)
    return img_bytes.getvalue()

def filter_masks_by_area(masks: List[dict], min_area: int = 100, max_area: Optional[int] = None) -> List[dict]:
    """
    Filter masks by area
    
    Args:
        masks: List of mask dictionaries
        min_area: Minimum area threshold
        max_area: Maximum area threshold (optional)
        
    Returns:
        Filtered mask list
    """
    filtered_masks = []
    
    for mask in masks:
        area = mask.get('area', 0)
        if area >= min_area:
            if max_area is None or area <= max_area:
                filtered_masks.append(mask)
    
    return filtered_masks

def sort_masks_by_area(masks: List[dict], reverse: bool = True) -> List[dict]:
    """
    Sort masks by area
    
    Args:
        masks: List of mask dictionaries
        reverse: If True, sort in descending order (largest first)
        
    Returns:
        Sorted mask list
    """
    return sorted(masks, key=lambda x: x.get('area', 0), reverse=reverse)

def find_mask_at_point(masks: List[dict], point: Tuple[int, int]) -> Optional[dict]:
    """
    Find the mask that contains the given point
    
    Args:
        masks: List of mask dictionaries
        point: (x, y) coordinates
        
    Returns:
        Mask dictionary or None if no mask contains the point
    """
    x, y = point
    
    # Sort masks by area (smallest first) to get the most specific mask
    sorted_masks = sort_masks_by_area(masks, reverse=False)
    
    for mask in sorted_masks:
        segmentation = mask['segmentation']
        if 0 <= y < segmentation.shape[0] and 0 <= x < segmentation.shape[1]:
            if segmentation[y, x]:
                return mask
    
    return None


def create_inpainting_mask(selected_masks: List[dict], image_shape: Tuple[int, ...]) -> np.ndarray:
    """
    Create inpainting mask from selected masks
    
    Args:
        selected_masks: List of selected mask dictionaries
        image_shape: (height, width) or (height, width, channels) of the target image
        
    Returns:
        Binary mask where 255 = inpaint, 0 = keep
    """
    # Handle both 2D and 3D image shapes
    if len(image_shape) == 3:
        height, width = image_shape[:2]
    else:
        height, width = image_shape
    inpaint_mask = np.zeros((height, width), dtype=np.uint8)
    
    for mask_dict in selected_masks:
        mask = mask_dict['segmentation']
        
        # Resize mask if needed
        if mask.shape != (height, width):
            mask = cv2.resize(mask.astype(np.uint8), (width, height), interpolation=cv2.INTER_NEAREST)
            mask = mask.astype(bool)
        
        # Add to inpainting mask
        inpaint_mask[mask] = 255
    
    return inpaint_mask


def create_inverse_mask(selected_masks: List[dict], image_shape: Tuple[int, ...]) -> np.ndarray:
    """
    Create inverse mask from selected masks (for showing what will be inpainted)
    
    Args:
        selected_masks: List of selected mask dictionaries
        image_shape: (height, width) or (height, width, channels) of the target image
        
    Returns:
        Binary mask where 255 = selected regions, 0 = other regions
    """
    return create_inpainting_mask(selected_masks, image_shape)


def visualize_inpainting_preview(image: np.ndarray, selected_masks: List[dict], alpha: float = 0.7) -> np.ndarray:
    """
    Visualize which regions will be inpainted
    
    Args:
        image: Original image as numpy array
        selected_masks: List of selected mask dictionaries  
        alpha: Transparency for overlay
        
    Returns:
        Image with inpainting regions highlighted
    """
    if not selected_masks:
        return image.copy()
    
    height, width = image.shape[:2]
    preview = image.copy()
    
    # Create overlay for selected regions
    overlay = np.zeros_like(image)
    
    for mask_dict in selected_masks:
        mask = mask_dict['segmentation']
        
        # Resize mask if needed
        if mask.shape != (height, width):
            mask = cv2.resize(mask.astype(np.uint8), (width, height), interpolation=cv2.INTER_NEAREST)
            mask = mask.astype(bool)
        
        # Add red overlay to selected regions
        overlay[mask] = [255, 100, 100]  # Red color
    
    # Blend overlay with original image
    mask_regions = np.any(overlay > 0, axis=2)
    preview[mask_regions] = cv2.addWeighted(
        image[mask_regions], 1-alpha, 
        overlay[mask_regions], alpha, 0
    )
    
    return preview


def resize_image_for_inpainting(image: Image.Image, max_size: int = 2048) -> Tuple[Image.Image, Tuple[int, int]]:
    """
    Resize image for inpainting if too large
    
    Args:
        image: PIL Image
        max_size: Maximum dimension
        
    Returns:
        Tuple of (resized_image, original_size)
    """
    original_size = image.size
    width, height = image.size
    
    if max(width, height) <= max_size:
        return image, original_size
    
    # Calculate new size maintaining aspect ratio
    if width > height:
        new_width = max_size
        new_height = int(height * max_size / width)
    else:
        new_height = max_size
        new_width = int(width * max_size / height)
    
    resized_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
    return resized_image, original_size


def apply_mask_to_image(image: np.ndarray, mask: np.ndarray) -> Image.Image:
    """
    Apply mask to image to create cutout
    
    Args:
        image: Image as numpy array
        mask: Binary mask
        
    Returns:
        PIL Image with transparent background
    """
    if len(image.shape) == 3:
        height, width, channels = image.shape
    else:
        height, width = image.shape
        channels = 1
    
    # Ensure mask is 2D
    if len(mask.shape) == 3:
        mask = mask[:, :, 0]
    
    # Resize mask if needed
    if mask.shape != (height, width):
        mask = cv2.resize(mask.astype(np.uint8), (width, height), interpolation=cv2.INTER_NEAREST)
        mask = mask.astype(bool)
    
    # Create RGBA image
    if channels == 3:
        rgba_image = np.zeros((height, width, 4), dtype=np.uint8)
        rgba_image[:, :, :3] = image
        rgba_image[:, :, 3] = mask.astype(np.uint8) * 255
    else:
        rgba_image = np.zeros((height, width, 4), dtype=np.uint8)
        rgba_image[:, :, 0] = image
        rgba_image[:, :, 1] = image
        rgba_image[:, :, 2] = image
        rgba_image[:, :, 3] = mask.astype(np.uint8) * 255
    
    return Image.fromarray(rgba_image, 'RGBA')


def prepare_image_for_inpainting(image: np.ndarray) -> Image.Image:
    """
    Prepare numpy image for inpainting
    
    Args:
        image: Image as numpy array
        
    Returns:
        PIL Image ready for inpainting
    """
    # Convert to PIL if numpy
    if isinstance(image, np.ndarray):
        if image.dtype != np.uint8:
            image = (image * 255).astype(np.uint8)
        pil_image = Image.fromarray(image)
    else:
        pil_image = image
    
    # Ensure RGB format
    if pil_image.mode != 'RGB':
        pil_image = pil_image.convert('RGB')
    
    return pil_image 