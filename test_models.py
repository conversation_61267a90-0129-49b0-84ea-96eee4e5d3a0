import numpy as np
from PIL import Image
import cv2
import torch
from sam_model import SAMPredictor
from matting_model import MODNetWrapper

def test_sam_model():
    """测试SAM模型"""
    print("\n" + "="*60)
    print("🧪 测试SAM2模型")
    print("="*60)
    
    try:
        # 测试不同的模型大小
        for model_size in ["tiny", "small"]:
            print(f"\n📊 测试 {model_size} 模型...")
            sam = SAMPredictor(model_size=model_size, use_gpu=True)
            
            # 创建测试图像
            test_image = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
            
            # 设置图像
            success = sam.set_image(test_image)
            if not success:
                print(f"❌ {model_size} 模型设置图像失败")
                continue
            
            # 测试自动分割
            masks = sam.predict_everything()
            print(f"✅ {model_size} 模型生成了 {len(masks)} 个masks")
            
            # 测试点击预测
            test_point = (256, 256)
            point_masks = sam.predict_point(test_point)
            print(f"✅ 点击预测生成了 {len(point_masks)} 个masks")
            
            # 获取模型信息
            info = sam.get_model_info()
            print(f"📊 模型信息: {info}")
            
    except Exception as e:
        print(f"❌ SAM模型测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_matting_model():
    """测试Matting模型"""
    print("\n" + "="*60)
    print("🧪 测试MODNet Matting模型")
    print("="*60)
    
    try:
        # 初始化模型
        matting = MODNetWrapper()
        
        # 获取模型信息
        info = matting.get_model_info()
        print(f"📊 模型信息: {info}")
        
        # 创建测试图像和mask
        test_image = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
        test_mask = np.zeros((512, 512), dtype=bool)
        test_mask[100:400, 100:400] = True
        
        # 测试matting
        matte = matting.predict(test_image, test_mask)
        print(f"✅ Matting完成，输出形状: {matte.shape}")
        
        # 测试trimap创建
        trimap = matting.create_trimap_from_mask(test_mask)
        print(f"✅ Trimap创建完成，形状: {trimap.shape}")
        
    except Exception as e:
        print(f"❌ Matting模型测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_gpu():
    """测试GPU状态"""
    print("\n" + "="*60)
    print("🧪 测试GPU状态")
    print("="*60)
    
    if torch.cuda.is_available():
        print(f"✅ CUDA可用")
        print(f"📊 GPU设备: {torch.cuda.get_device_name(0)}")
        print(f"📊 GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
        print(f"📊 CUDA版本: {torch.version.cuda}")
        print(f"📊 PyTorch版本: {torch.__version__}")
        
        # 测试GPU计算
        try:
            test_tensor = torch.randn(1000, 1000).cuda()
            result = torch.mm(test_tensor, test_tensor.t())
            print("✅ GPU计算测试通过")
        except Exception as e:
            print(f"❌ GPU计算测试失败: {e}")
    else:
        print("❌ CUDA不可用，使用CPU模式")

def main():
    """运行所有测试"""
    print("🚀 开始模型测试...")
    
    # 测试GPU
    test_gpu()
    
    # 测试SAM模型
    test_sam_model()
    
    # 测试Matting模型
    test_matting_model()
    
    print("\n✅ 所有测试完成！")

if __name__ == "__main__":
    main() 