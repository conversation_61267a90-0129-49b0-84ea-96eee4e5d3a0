#!/usr/bin/env python3
"""
GPU测试脚本 - 测试SAM和MODNet模型在GPU上的运行情况
"""

import torch
import time
import numpy as np
from PIL import Image
import os

def test_gpu_basic():
    """基础GPU测试"""
    print("=" * 50)
    print("🔍 基础GPU测试")
    print("=" * 50)
    
    print(f"CUDA可用: {torch.cuda.is_available()}")
    print(f"CUDA设备数量: {torch.cuda.device_count()}")
    print(f"当前设备: {torch.cuda.current_device()}")
    print(f"设备名称: {torch.cuda.get_device_name(0)}")
    
    if torch.cuda.is_available():
        # 测试GPU内存
        print(f"GPU总内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.2f} GB")
        print(f"GPU已用内存: {torch.cuda.memory_allocated(0) / 1024**3:.2f} GB")
        print(f"GPU缓存内存: {torch.cuda.memory_reserved(0) / 1024**3:.2f} GB")
        
        # 测试GPU计算
        device = torch.device('cuda')
        x = torch.randn(1000, 1000).to(device)
        y = torch.randn(1000, 1000).to(device)
        
        start_time = time.time()
        z = torch.mm(x, y)
        torch.cuda.synchronize()
        gpu_time = time.time() - start_time
        
        print(f"GPU矩阵乘法耗时: {gpu_time:.4f} 秒")
        print("✅ 基础GPU测试通过")
        return True
    else:
        print("❌ CUDA不可用")
        return False

def test_sam_gpu():
    """测试SAM模型GPU加速"""
    print("\n" + "=" * 50)
    print("🤖 SAM模型GPU测试")
    print("=" * 50)
    
    try:
        from sam_model import SAMPredictor
        
        # 测试CPU
        print("🔄 测试SAM模型CPU模式...")
        start_time = time.time()
        predictor_cpu = SAMPredictor(use_gpu=False)
        cpu_load_time = time.time() - start_time
        print(f"CPU模型加载耗时: {cpu_load_time:.2f} 秒")
        
        # 测试GPU
        print("🔄 测试SAM模型GPU模式...")
        start_time = time.time()
        predictor_gpu = SAMPredictor(use_gpu=True)
        gpu_load_time = time.time() - start_time
        print(f"GPU模型加载耗时: {gpu_load_time:.2f} 秒")
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
        test_image = Image.fromarray(test_image)
        
        # CPU推理测试
        print("🔄 CPU推理测试...")
        predictor_cpu.set_image(test_image)
        start_time = time.time()
        masks_cpu = predictor_cpu.predict_everything()
        cpu_inference_time = time.time() - start_time
        print(f"CPU推理耗时: {cpu_inference_time:.2f} 秒，生成 {len(masks_cpu)} 个mask")
        
        # GPU推理测试
        print("🔄 GPU推理测试...")
        predictor_gpu.set_image(test_image)
        start_time = time.time()
        masks_gpu = predictor_gpu.predict_everything()
        torch.cuda.synchronize()
        gpu_inference_time = time.time() - start_time
        print(f"GPU推理耗时: {gpu_inference_time:.2f} 秒，生成 {len(masks_gpu)} 个mask")
        
        # 性能对比
        if gpu_inference_time > 0:
            speedup = cpu_inference_time / gpu_inference_time
            print(f"🚀 GPU加速比: {speedup:.2f}x")
        
        print("✅ SAM模型GPU测试完成")
        return True
        
    except Exception as e:
        print(f"❌ SAM模型GPU测试失败: {e}")
        return False

def test_modnet_gpu():
    """测试MODNet模型GPU加速"""
    print("\n" + "=" * 50)
    print("🎨 MODNet模型GPU测试")
    print("=" * 50)
    
    try:
        from matting_model import MODNetWrapper
        
        # 检查权重文件
        ckpt_path = "checkpoints/modnet_photographic_portrait_matting.pth"
        if not os.path.exists(ckpt_path):
            print(f"⚠️ 权重文件不存在: {ckpt_path}")
            print("请先下载MODNet权重文件")
            return False
        
        # 测试CPU
        print("🔄 测试MODNet模型CPU模式...")
        start_time = time.time()
        matting_cpu = MODNetWrapper(use_gpu=False, ckpt_path=ckpt_path)
        cpu_load_time = time.time() - start_time
        print(f"CPU模型加载耗时: {cpu_load_time:.2f} 秒")
        print(f"CPU模型类型: {matting_cpu.get_model_info()['model_type']}")
        
        # 测试GPU
        print("🔄 测试MODNet模型GPU模式...")
        start_time = time.time()
        matting_gpu = MODNetWrapper(use_gpu=True, ckpt_path=ckpt_path)
        gpu_load_time = time.time() - start_time
        print(f"GPU模型加载耗时: {gpu_load_time:.2f} 秒")
        print(f"GPU模型类型: {matting_gpu.get_model_info()['model_type']}")
        
        # 创建测试图像和mask
        test_image = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
        test_mask = np.random.randint(0, 2, (512, 512), dtype=np.uint8)
        
        # CPU推理测试
        print("🔄 CPU推理测试...")
        start_time = time.time()
        matte_cpu = matting_cpu.predict(test_image, test_mask)
        cpu_inference_time = time.time() - start_time
        print(f"CPU推理耗时: {cpu_inference_time:.2f} 秒")
        
        # GPU推理测试
        print("🔄 GPU推理测试...")
        start_time = time.time()
        matte_gpu = matting_gpu.predict(test_image, test_mask)
        torch.cuda.synchronize()
        gpu_inference_time = time.time() - start_time
        print(f"GPU推理耗时: {gpu_inference_time:.2f} 秒")
        
        # 性能对比
        if gpu_inference_time > 0:
            speedup = cpu_inference_time / gpu_inference_time
            print(f"🚀 GPU加速比: {speedup:.2f}x")
        
        print("✅ MODNet模型GPU测试完成")
        return True
        
    except Exception as e:
        print(f"❌ MODNet模型GPU测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_memory_usage():
    """测试GPU内存使用情况"""
    print("\n" + "=" * 50)
    print("💾 GPU内存使用测试")
    print("=" * 50)
    
    if torch.cuda.is_available():
        print("初始内存状态:")
        print(f"  已分配: {torch.cuda.memory_allocated(0) / 1024**2:.1f} MB")
        print(f"  已缓存: {torch.cuda.memory_reserved(0) / 1024**2:.1f} MB")
        
        # 分配一些内存
        tensors = []
        for i in range(5):
            tensor = torch.randn(1000, 1000).cuda()
            tensors.append(tensor)
            print(f"分配第{i+1}个tensor后:")
            print(f"  已分配: {torch.cuda.memory_allocated(0) / 1024**2:.1f} MB")
            print(f"  已缓存: {torch.cuda.memory_reserved(0) / 1024**2:.1f} MB")
        
        # 清理内存
        del tensors
        torch.cuda.empty_cache()
        print("清理内存后:")
        print(f"  已分配: {torch.cuda.memory_allocated(0) / 1024**2:.1f} MB")
        print(f"  已缓存: {torch.cuda.memory_reserved(0) / 1024**2:.1f} MB")
        
        print("✅ GPU内存测试完成")
        return True
    else:
        print("❌ CUDA不可用，跳过内存测试")
        return False

def main():
    """主测试函数"""
    print("🚀 开始GPU加速测试")
    print("=" * 60)
    
    # 基础GPU测试
    gpu_ok = test_gpu_basic()
    
    if gpu_ok:
        # SAM模型测试
        sam_ok = test_sam_gpu()
        
        # MODNet模型测试
        modnet_ok = test_modnet_gpu()
        
        # 内存测试
        memory_ok = test_memory_usage()
        
        # 总结
        print("\n" + "=" * 60)
        print("📊 测试结果总结")
        print("=" * 60)
        print(f"✅ GPU基础功能: {'通过' if gpu_ok else '失败'}")
        print(f"✅ SAM模型GPU加速: {'通过' if sam_ok else '失败'}")
        print(f"✅ MODNet模型GPU加速: {'通过' if modnet_ok else '失败'}")
        print(f"✅ GPU内存管理: {'通过' if memory_ok else '失败'}")
        
        if gpu_ok and sam_ok and modnet_ok:
            print("\n🎉 所有GPU测试通过！可以启用GPU加速模式。")
            print("💡 建议在app_stable.py中设置use_gpu=True")
        else:
            print("\n⚠️ 部分测试失败，建议检查模型文件和环境配置。")
    else:
        print("\n❌ GPU基础测试失败，无法进行模型测试。")

if __name__ == "__main__":
    main() 