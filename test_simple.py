import numpy as np
import cv2
from PIL import Image
from utils import visualize_masks

def test_simple():
    """简单测试"""
    print("🧪 简单测试...")
    
    # 创建测试图像和masks
    image = np.random.randint(0, 255, (800, 600, 3), dtype=np.uint8)
    
    # 创建不同尺寸的masks
    masks = []
    
    # 正确尺寸的mask
    mask1 = np.zeros((800, 600), dtype=bool)
    mask1[100:300, 100:400] = True
    masks.append({'segmentation': mask1})
    
    # 错误尺寸的mask (1920x1080)
    mask2 = np.zeros((1920, 1080), dtype=bool)
    mask2[200:600, 200:800] = True
    masks.append({'segmentation': mask2})
    
    try:
        result = visualize_masks(image, masks)
        print("✅ 测试通过！")
        print(f"   输入: {image.shape}")
        print(f"   输出: {result.shape}")
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple()
    if success:
        print("\n🎉 修复成功！")
    else:
        print("\n💥 修复失败！") 