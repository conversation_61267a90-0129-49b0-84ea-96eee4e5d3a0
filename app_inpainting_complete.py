import gradio as gr
import numpy as np
from PIL import Image
import traceback
import tempfile
import os
import cv2
import time
import sys
import subprocess

def check_and_install_missing_dependencies():
    """检查并安装缺失的依赖"""
    missing_deps = []
    
    try:
        import skimage
    except ImportError:
        missing_deps.append("scikit-image")
    
    if missing_deps:
        print("🔍 检测到缺失的依赖包:")
        for dep in missing_deps:
            print(f"   - {dep}")
        
        print("\n📦 正在自动安装缺失的依赖...")
        
        for dep in missing_deps:
            try:
                print(f"安装 {dep}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
                print(f"✅ {dep} 安装成功")
            except subprocess.CalledProcessError as e:
                print(f"❌ {dep} 安装失败: {e}")
                print(f"💡 请手动运行: pip install {dep}")
                return False
        
        print("✅ 所有依赖安装完成！正在重新导入...")
        return True
    
    return True

# 检查依赖
if not check_and_install_missing_dependencies():
    print("\n❌ 依赖安装失败，请手动安装缺失的包后重试")
    print("建议命令:")
    print("  pip install scikit-image")
    print("  pip install -r requirements.txt")
    sys.exit(1)

# Import our custom modules
try:
    from sam_model import SAMPredictor
    from matting_model import MODNetWrapper, create_rgba_with_matte
    from inpainting_model import LAMAInpainter, simple_inpaint
    from utils import (
        pil_to_numpy, numpy_to_pil, visualize_masks, visualize_masks_with_borders,
        find_mask_at_point, filter_masks_by_area, resize_image_for_display, merge_masks,
        create_inpainting_mask, visualize_inpainting_preview
    )
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("💡 请确保所有依赖都已正确安装")
    print("建议运行: pip install -r requirements.txt")
    sys.exit(1)

class AIImageInpaintingComplete:
    def __init__(self, model_size: str = "tiny"):
        """
        Initialize the AI Image Inpainting Complete application
        
        Args:
            model_size: SAM model size ('tiny', 'small', 'base_plus', 'large')
        """
        self.model_size = model_size
        self.predictor = None
        self.matting_model = None
        self.inpainting_model = None
        self.current_image = None  # 原始图像
        self.display_image = None  # 调整后的显示图像
        self.current_masks = []
        self.selected_masks = []  # 存储选中的区域用于补全
        self.segmentation_image = None
        self.last_output_file = None  # 保存最后生成的文件路径
        self.mode = "cutout"  # "cutout" or "inpainting"
        self.last_clicked_mask = None  # 保存最后点击的mask，用于参数更新时重新生成
        
        # 补全参数
        self.inpainting_method = "telea"  # 补全算法
        self.inpainting_radius = 5  # 补全半径
        
        # Matting 参数
        self.use_matting = True
        self.trimap_size = 10
        self.matting_quality = "balanced"
        self.detail_enhancement = 1.0
        
        # Initialize models
        self._init_models()
    
    def _init_models(self):
        """Initialize all models"""
        try:
            # 初始化 SAM 模型
            print(f"Loading SAM2 {self.model_size} model...")
            self.predictor = SAMPredictor(model_size=self.model_size)
            print("SAM model loaded successfully!")
            
            # 初始化 Matting 模型
            print("Loading Matting model...")
            self.matting_model = MODNetWrapper()
            print("Matting model loaded successfully!")
            
            # 初始化 LAMA 补全模型
            print("Loading LAMA inpainting model...")
            self.inpainting_model = LAMAInpainter()
            print("Inpainting model loaded successfully!")
            
        except Exception as e:
            print(f"Error loading models: {e}")
            self.predictor = None
            self.matting_model = None
            self.inpainting_model = None
    
    def upload_image(self, image: Image.Image) -> tuple:
        """Handle image upload"""
        if image is None:
            return None, "请先上传图片"
        
        if self.predictor is None:
            return None, "模型未加载成功，请检查依赖安装"
        
        try:
            # Reset selections
            self.selected_masks = []
            
            # Convert and save
            self.current_image = pil_to_numpy(image)
            self.display_image = resize_image_for_display(self.current_image, max_size=800)
            
            # Set image for SAM predictor
            self.predictor.set_image(self.display_image)
            
            # Generate masks
            print("Generating masks...")
            masks = self.predictor.predict_everything()
            filtered_masks = filter_masks_by_area(masks, min_area=500)
            self.current_masks = filtered_masks
            
            if not filtered_masks:
                return numpy_to_pil(self.display_image), "未检测到足够大的区域，请尝试其他图片"
            
            # Visualize masks
            self.segmentation_image = visualize_masks_with_borders(
                self.display_image, filtered_masks, self.selected_masks
            )
            
            status_msg = f"成功检测到 {len(filtered_masks)} 个可选区域 | 当前模式: {self.mode} | SAM: {self.model_size}"
            return numpy_to_pil(self.segmentation_image), status_msg
            
        except Exception as e:
            print(f"Error uploading image: {e}")
            traceback.print_exc()
            return None, f"上传图片失败: {str(e)}"
    
    def on_image_click(self, evt: gr.SelectData) -> tuple:
        """Handle click on segmentation image"""
        if not self.current_masks:
            return None, None, "请先上传图片", None
        
        try:
            x, y = evt.index
            print(f"Click at: ({x}, {y}) | Mode: {self.mode}")
            
            clicked_mask = find_mask_at_point(self.current_masks, (x, y))
            
            if clicked_mask is None:
                return numpy_to_pil(self.segmentation_image), None, "未找到该位置的区域", None
            
            if self.mode == "cutout":
                return self._process_cutout(clicked_mask)
            else:  # inpainting mode
                return self._process_inpainting_selection(clicked_mask)
                
        except Exception as e:
            print(f"Error processing click: {e}")
            traceback.print_exc()
            return None, None, f"处理失败: {str(e)}", None
    
    def _process_cutout(self, clicked_mask: dict) -> tuple:
        """处理抠图模式"""
        try:
            if self.current_image is None or self.display_image is None:
                return None, None, "图像未加载，请先上传图片", None
            
            # 保存最后点击的mask
            self.last_clicked_mask = clicked_mask
            
            # Get and resize mask
            mask = clicked_mask['segmentation']
            original_h, original_w = self.current_image.shape[:2]
            display_h, display_w = self.display_image.shape[:2]
            
            if original_h != display_h or original_w != display_w:
                mask = cv2.resize(
                    mask.astype(np.uint8), 
                    (original_w, original_h), 
                    interpolation=cv2.INTER_NEAREST
                ).astype(bool)
            
            # Create cutout using matting
            if self.use_matting and self.matting_model is not None:
                matte = self.matting_model.predict(
                    self.current_image,
                    mask,
                    trimap_size=self.trimap_size,
                    quality=self.matting_quality
                )
                
                if self.detail_enhancement != 1.0:
                    matte = self.matting_model.enhance_details(matte, self.detail_enhancement)
                
                cutout_image = create_rgba_with_matte(self.current_image, matte)
            else:
                from utils import create_cutout_image
                cutout_image = create_cutout_image(
                    self.current_image, 
                    mask,
                    refine_edges=True,
                    edge_method="comprehensive",
                    smooth_strength=3,
                    feather_radius=3
                )
            
            # Save result
            timestamp = int(time.time())
            output_filename = f"cutout_{timestamp}.png"
            output_path = os.path.join("outputs", output_filename)
            os.makedirs("outputs", exist_ok=True)
            
            cutout_image.save(output_path, 'PNG')
            self.last_output_file = output_path
            
            status_msg = f"抠图完成！面积: {clicked_mask['area']} 像素"
            if self.use_matting:
                status_msg += f" | Matting: {self.matting_quality}"
            
            return numpy_to_pil(self.segmentation_image), cutout_image, status_msg, output_path
            
        except Exception as e:
            error_msg = f"抠图失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return None, None, error_msg, None
    
    def _process_inpainting_selection(self, clicked_mask: dict) -> tuple:
        """处理补全模式的区域选择"""
        try:
            if self.display_image is None:
                return None, None, "图像未加载，请先上传图片", None
            
            # Toggle selection
            mask_id = id(clicked_mask)
            already_selected = any(id(mask) == mask_id for mask in self.selected_masks)
            
            if already_selected:
                self.selected_masks = [mask for mask in self.selected_masks if id(mask) != mask_id]
                action = "取消选择"
            else:
                self.selected_masks.append(clicked_mask)
                action = "选择"
            
            # Update visualization with inpainting preview
            if self.selected_masks:
                self.segmentation_image = visualize_inpainting_preview(
                    self.display_image, self.selected_masks, alpha=0.7
                )
            else:
                self.segmentation_image = visualize_masks_with_borders(
                    self.display_image, self.current_masks, self.selected_masks
                )
            
            status_msg = f"补全模式：{action}区域。已选择 {len(self.selected_masks)} 个区域待补全"
            return numpy_to_pil(self.segmentation_image), None, status_msg, None
            
        except Exception as e:
            error_msg = f"选择处理失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return None, None, error_msg, None
    
    def execute_inpainting(self) -> tuple:
        """执行图像补全"""
        if not self.selected_masks:
            return None, "没有选中任何区域，请先选择需要补全的区域", None
        
        if self.current_image is None:
            return None, "请先上传图片", None
        
        try:
            # Create inpainting mask
            mask = create_inpainting_mask(self.selected_masks, self.current_image.shape[:2])
            
            # Scale mask to original image size if needed
            original_h, original_w = self.current_image.shape[:2]
            display_h, display_w = self.display_image.shape[:2]
            
            if original_h != display_h or original_w != display_w:
                mask = cv2.resize(
                    mask.astype(np.uint8), 
                    (original_w, original_h), 
                    interpolation=cv2.INTER_NEAREST
                ).astype(bool)
            
            # Convert mask to uint8
            mask_uint8 = (mask * 255).astype(np.uint8)
            
            # Perform inpainting
            if self.inpainting_model is not None:
                result_image = self.inpainting_model.inpaint(
                    self.current_image,
                    mask_uint8,
                    method=self.inpainting_method
                )
            else:
                result_image = simple_inpaint(
                    self.current_image,
                    mask_uint8,
                    method=self.inpainting_method
                )
            
            # Save result
            timestamp = int(time.time())
            output_filename = f"inpaint_result_{timestamp}.png"
            output_path = os.path.join("outputs", output_filename)
            os.makedirs("outputs", exist_ok=True)
            
            result_image.save(output_path, 'PNG')
            self.last_output_file = output_path
            
            status_msg = f"图像补全完成！补全了 {len(self.selected_masks)} 个区域 | 方法: {self.inpainting_method}"
            return result_image, status_msg, output_path
            
        except Exception as e:
            error_msg = f"图像补全失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return None, error_msg, None
    
    def switch_mode(self, new_mode: str) -> tuple:
        """切换工作模式"""
        self.mode = new_mode
        self.selected_masks = []  # 切换模式时清空选择
        
        # Update visualization
        if self.segmentation_image is not None and self.display_image is not None:
            self.segmentation_image = visualize_masks_with_borders(
                self.display_image, self.current_masks, self.selected_masks
            )
        
        mode_names = {"cutout": "抠图模式", "inpainting": "补全模式"}
        status_msg = f"已切换到{mode_names.get(new_mode, new_mode)}"
        
        if self.segmentation_image is not None:
            return numpy_to_pil(self.segmentation_image), status_msg
        else:
            return None, status_msg
    
    def clear_selection(self) -> tuple:
        """清空选择"""
        self.selected_masks = []
        
        if self.segmentation_image is not None and self.display_image is not None:
            self.segmentation_image = visualize_masks_with_borders(
                self.display_image, self.current_masks, self.selected_masks
            )
            return numpy_to_pil(self.segmentation_image), "已清空所有选择"
        
        return None, "已清空所有选择"
    
    def update_inpainting_params(self, method: str, radius: int) -> str:
        """更新补全参数"""
        self.inpainting_method = method
        self.inpainting_radius = radius
        return f"补全参数已更新: 方法={method}, 半径={radius}"
    
    def update_matting_params_realtime(self, use_matting: bool, trimap_size: int, 
                                      quality: str, enhancement: float) -> tuple:
        """实时更新Matting参数并重新生成抠图"""
        # 更新参数
        self.use_matting = use_matting
        self.trimap_size = trimap_size
        self.matting_quality = quality
        self.detail_enhancement = enhancement
        
        status = f"Matting参数已更新: "
        if use_matting:
            status += f"开启, trimap={trimap_size}, 质量={quality}, 增强={enhancement:.1f}"
        else:
            status += "关闭（使用传统方法）"
        
        # 如果有最后点击的区域且在抠图模式，重新生成抠图
        if (self.mode == "cutout" and self.last_clicked_mask is not None and self.current_image is not None):
            try:
                # 使用最后点击的mask重新生成
                cutout_result = self._process_cutout(self.last_clicked_mask)
                if cutout_result[1] is not None:  # 如果生成成功
                    status += " | 抠图已实时更新"
                    return cutout_result[1], status, cutout_result[3]  # result_image, status, download_file
            except Exception as e:
                status += f" | 更新失败: {str(e)}"
        
        return None, status, None
    
    def update_matting_params(self, use_matting: bool, trimap_size: int, 
                            quality: str, enhancement: float) -> str:
        """更新Matting参数（仅状态更新）"""
        self.use_matting = use_matting
        self.trimap_size = trimap_size
        self.matting_quality = quality
        self.detail_enhancement = enhancement
        
        status = f"Matting参数已更新: "
        if use_matting:
            status += f"开启, trimap={trimap_size}, 质量={quality}, 增强={enhancement:.1f}"
        else:
            status += "关闭"
        
        return status
    
    def change_sam_model(self, new_model_size: str, image: Image.Image) -> tuple:
        """更换SAM模型并重新分析图片"""
        try:
            if new_model_size == self.model_size:
                return None, f"当前已是 {new_model_size} 模型"
            
            # 更新模型大小
            self.model_size = new_model_size
            
            # 重新初始化SAM模型
            print(f"切换到 SAM2 {new_model_size} 模型...")
            self.predictor = SAMPredictor(model_size=new_model_size)
            print("SAM model reloaded successfully!")
            
            # 如果有图片，重新分析
            if image is not None:
                return self.upload_image(image)
            else:
                return None, f"已切换到 {new_model_size} 模型，请上传图片"
                
        except Exception as e:
            error_msg = f"切换模型失败: {str(e)}"
            print(error_msg)
            return None, error_msg


def create_interface():
    """Create the complete inpainting interface"""
    
    app = AIImageInpaintingComplete(model_size="tiny")
    
    with gr.Blocks(title="AI智能抠图与补全工具 - 完整版") as demo:
        gr.Markdown("""
        # 🎨 AI智能抠图与补全工具 - 完整版
        
        ## 🚀 双模式功能：
        - **🖼️ 抠图模式**：SAM + Matting 高质量抠图
        - **🔧 补全模式**：LAMA 智能图像修复/补全
        
        ## 📋 使用说明：
        1. 上传图片，系统自动检测可选区域
        2. 选择工作模式（抠图/补全）
        3. 点击区域进行操作
        4. 下载处理结果
        
        ### 抠图模式：点击区域直接抠图
        ### 补全模式：选择多个区域 → 点击"执行补全"
        """)
        
        with gr.Row():
            with gr.Column(scale=1):
                # 图片上传
                input_image = gr.Image(
                    label="上传图片",
                    type="pil",
                    elem_id="input_image"
                )
                
                # 模式选择
                with gr.Row():
                    mode_selector = gr.Radio(
                        choices=["cutout", "inpainting"],
                        value="cutout",
                        label="工作模式",
                        info="抠图：提取对象 | 补全：修复/删除对象"
                    )
                
                # SAM 模型选择
                with gr.Row():
                    model_selector = gr.Dropdown(
                        choices=["tiny", "small", "base_plus", "large"],
                        value="tiny",
                        label="SAM 模型",
                        info="tiny最快，large最准确"
                    )
                
                # 补全设置
                with gr.Accordion("🔧 补全设置", open=True):
                    inpainting_method = gr.Radio(
                        choices=["telea", "ns", "fast_marching"],
                        value="telea",
                        label="补全算法",
                        info="telea: 小面积 | ns: 线性结构 | fast_marching: 快速"
                    )
                    
                    inpainting_radius = gr.Slider(
                        minimum=1,
                        maximum=20,
                        step=1,
                        value=5,
                        label="补全半径",
                        info="影响补全的范围"
                    )
                
                # Matting 设置
                with gr.Accordion("🎨 Matting 设置", open=False):
                    use_matting = gr.Checkbox(
                        value=True,
                        label="启用 Matting 增强",
                        info="仅对抠图模式有效"
                    )
                    
                    with gr.Row():
                        trimap_size = gr.Slider(
                            minimum=5,
                            maximum=30,
                            step=5,
                            value=10,
                            label="边缘过渡区域"
                        )
                        
                        matting_quality = gr.Radio(
                            choices=["fast", "balanced", "high"],
                            value="balanced",
                            label="处理质量"
                        )
                    
                    detail_enhancement = gr.Slider(
                        minimum=0.5,
                        maximum=2.0,
                        step=0.1,
                        value=1.0,
                        label="细节增强"
                    )
                
                # 操作按钮
                with gr.Row():
                    clear_btn = gr.Button("清空选择", size="sm")
                    inpaint_btn = gr.Button("执行补全", variant="primary", size="sm")
                
                # 状态信息
                status_text = gr.Textbox(
                    label="状态",
                    interactive=False,
                    value="请上传图片开始"
                )
            
            with gr.Column(scale=1):
                # 分割结果展示
                segmentation_output = gr.Image(
                    label="点击选择区域",
                    type="pil",
                    interactive=True,
                    elem_id="segmentation_output"
                )
                
                # 处理结果
                result_output = gr.Image(
                    label="处理结果",
                    type="pil",
                    elem_id="result_output"
                )
                
                # 下载按钮
                download_btn = gr.File(
                    label="下载结果文件",
                    visible=True,
                    elem_id="download_btn"
                )
        
        # 事件处理
        
        # 上传图片
        input_image.upload(
            fn=app.upload_image,
            inputs=[input_image],
            outputs=[segmentation_output, status_text]
        )
        
        # 点击分割图像
        segmentation_output.select(
            fn=app.on_image_click,
            inputs=[],
            outputs=[segmentation_output, result_output, status_text, download_btn]
        )
        
        # 切换模式
        mode_selector.change(
            fn=app.switch_mode,
            inputs=[mode_selector],
            outputs=[segmentation_output, status_text]
        )
        
        # 清空选择
        clear_btn.click(
            fn=app.clear_selection,
            inputs=[],
            outputs=[segmentation_output, status_text]
        )
        
        # 执行补全
        inpaint_btn.click(
            fn=app.execute_inpainting,
            inputs=[],
            outputs=[result_output, status_text, download_btn]
        )
        
        # 更新补全参数
        for component in [inpainting_method, inpainting_radius]:
            component.change(
                fn=app.update_inpainting_params,
                inputs=[inpainting_method, inpainting_radius],
                outputs=[status_text]
            )
        
        # SAM模型切换
        model_selector.change(
            fn=app.change_sam_model,
            inputs=[model_selector, input_image],
            outputs=[segmentation_output, status_text]
        )
        
        # 图片变化时也重新分析
        input_image.change(
            fn=app.upload_image,
            inputs=[input_image],
            outputs=[segmentation_output, status_text]
        )
        
        # 更新Matting参数（实时更新）
        for component in [use_matting, trimap_size, matting_quality, detail_enhancement]:
            component.change(
                fn=app.update_matting_params_realtime,
                inputs=[use_matting, trimap_size, matting_quality, detail_enhancement],
                outputs=[result_output, status_text, download_btn]
            )
    
    return demo


def main():
    """Main function"""
    print("🚀 启动AI智能抠图与补全工具 - 完整版...")
    
    # 清除可能的环境变量
    if 'GRADIO_SERVER_PORT' in os.environ:
        del os.environ['GRADIO_SERVER_PORT']
    
    demo = create_interface()
    
    # 尝试多个端口，找到可用的
    ports_to_try = [7870, 7871, 7872, 7873, 7874, 7875, 7876, 7877, 7878, 7879]
    
    for port in ports_to_try:
        try:
            print(f"🔍 尝试启动在端口 {port}...")
            demo.launch(
                server_name="0.0.0.0",
                server_port=port,
                share=False,
                show_error=True,
                quiet=False
            )
            print(f"✅ 应用成功启动在端口 {port}")
            print(f"🌐 请在浏览器中访问: http://localhost:{port}")
            break
        except OSError as e:
            if "Cannot find empty port" in str(e):
                print(f"⚠️  端口 {port} 被占用，尝试下一个...")
                continue
            else:
                print(f"❌ 启动失败: {e}")
                raise e
    else:
        print("❌ 无法找到可用端口，请检查是否有其他应用占用了端口")
        print("💡 您可以先关闭其他 Gradio 应用，然后重试")
        print("💡 或者手动指定端口: $env:GRADIO_SERVER_PORT=7880; python app_inpainting_complete.py")


if __name__ == "__main__":
    main()