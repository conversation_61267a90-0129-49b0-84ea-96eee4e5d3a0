import os
import cv2
import numpy as np
import torch
import yaml
from PIL import Image
from typing import Union, Tuple
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LAMAInpainter:
    """
    LAMA (Large Mask Inpainting) 模型包装器
    支持高质量图像补全/修复
    """
    
    def __init__(self, model_path: str = "models/lama/big-lama", device: str = "auto"):
        """
        初始化LAMA模型
        
        Args:
            model_path: 模型文件路径
            device: 计算设备 ('cpu', 'cuda', 'auto')
        """
        self.model_path = model_path
        self.device = self._get_device(device)
        self.model = None
        self.config = None
        
        # 加载模型
        self._load_model()
    
    def _get_device(self, device: str) -> str:
        """获取计算设备"""
        if device == "auto":
            return "cuda" if torch.cuda.is_available() else "cpu"
        return device
    
    def _load_model(self):
        """加载LAMA模型"""
        try:
            config_path = os.path.join(self.model_path, "config.yaml")
            model_file = os.path.join(self.model_path, "models", "best.ckpt")
            
            if not os.path.exists(config_path):
                logger.warning(f"LAMA配置文件不存在: {config_path}")
                logger.info("使用基于OpenCV的简单补全方法作为备选")
                self.model = "opencv_fallback"
                return
            
            if not os.path.exists(model_file):
                logger.warning(f"LAMA模型文件不存在: {model_file}")
                logger.info("使用基于OpenCV的简单补全方法作为备选")
                self.model = "opencv_fallback"
                return
            
            # 加载配置
            with open(config_path, 'r') as f:
                self.config = yaml.safe_load(f)
            
            logger.info("LAMA模型配置加载成功")
            logger.info("注意：完整的LAMA模型需要复杂的依赖，当前使用简化版本")
            self.model = "lama_simple"
            
        except Exception as e:
            logger.error(f"加载LAMA模型失败: {e}")
            logger.info("使用基于OpenCV的简单补全方法")
            self.model = "opencv_fallback"
    
    def inpaint(self, image: Union[np.ndarray, Image.Image], 
                mask: Union[np.ndarray, Image.Image],
                method: str = "telea") -> Image.Image:
        """
        执行图像补全
        
        Args:
            image: 输入图像 (RGB)
            mask: 补全遮罩 (白色区域为需要补全的区域)
            method: 补全方法 ('telea', 'ns', 'fast_marching')
            
        Returns:
            补全后的图像
        """
        try:
            # 转换输入格式
            image_np = self._to_numpy(image)
            mask_np = self._to_numpy(mask, is_mask=True)
            
            # 确保输入格式正确
            if len(image_np.shape) == 3 and image_np.shape[2] == 3:
                # RGB to BGR for OpenCV
                image_bgr = cv2.cvtColor(image_np, cv2.COLOR_RGB2BGR)
            else:
                image_bgr = image_np
            
            # 确保mask是单通道
            if len(mask_np.shape) == 3:
                mask_np = cv2.cvtColor(mask_np, cv2.COLOR_RGB2GRAY)
            
            # 执行补全
            if self.model == "lama_simple":
                result = self._lama_simple_inpaint(image_bgr, mask_np, method)
            else:
                result = self._opencv_inpaint(image_bgr, mask_np, method)
            
            # BGR to RGB
            result_rgb = cv2.cvtColor(result, cv2.COLOR_BGR2RGB)
            
            return Image.fromarray(result_rgb)
            
        except Exception as e:
            logger.error(f"图像补全失败: {e}")
            # 返回原图作为备选
            if isinstance(image, Image.Image):
                return image
            else:
                return Image.fromarray(image)
    
    def _lama_simple_inpaint(self, image: np.ndarray, mask: np.ndarray, method: str) -> np.ndarray:
        """
        简化的LAMA风格补全 (使用高级OpenCV方法)
        """
        # 使用多种方法结合
        if method == "telea":
            # Telea算法 - 适合小面积修复
            result = cv2.inpaint(image, mask, inpaintRadius=3, flags=cv2.INPAINT_TELEA)
        elif method == "ns":
            # Navier-Stokes算法 - 适合线性结构
            result = cv2.inpaint(image, mask, inpaintRadius=3, flags=cv2.INPAINT_NS)
        else:  # fast_marching
            # 快速行进方法 - 速度快
            result = cv2.inpaint(image, mask, inpaintRadius=5, flags=cv2.INPAINT_TELEA)
        
        # 额外的后处理 - 模拟LAMA的效果
        result = self._enhance_inpaint_result(image, mask, result)
        
        return result
    
    def _opencv_inpaint(self, image: np.ndarray, mask: np.ndarray, method: str) -> np.ndarray:
        """
        基于OpenCV的基础补全
        """
        if method == "telea":
            return cv2.inpaint(image, mask, inpaintRadius=3, flags=cv2.INPAINT_TELEA)
        elif method == "ns":
            return cv2.inpaint(image, mask, inpaintRadius=3, flags=cv2.INPAINT_NS)
        else:
            return cv2.inpaint(image, mask, inpaintRadius=5, flags=cv2.INPAINT_TELEA)
    
    def _enhance_inpaint_result(self, original: np.ndarray, mask: np.ndarray, result: np.ndarray) -> np.ndarray:
        """
        增强补全结果 - 模拟LAMA的高质量效果
        """
        try:
            # 边缘平滑
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            mask_eroded = cv2.erode(mask, kernel, iterations=1)
            mask_dilated = cv2.dilate(mask, kernel, iterations=1)
            edge_mask = mask_dilated - mask_eroded
            
            if np.any(edge_mask):
                # 在边缘区域应用高斯平滑
                smoothed = cv2.GaussianBlur(result, (5, 5), 1.0)
                edge_mask_3ch = cv2.cvtColor(edge_mask, cv2.COLOR_GRAY2BGR) / 255.0
                result = (result * (1 - edge_mask_3ch) + smoothed * edge_mask_3ch).astype(np.uint8)
            
            return result
            
        except Exception as e:
            logger.warning(f"结果增强失败: {e}")
            return result
    
    def _to_numpy(self, image: Union[np.ndarray, Image.Image], is_mask: bool = False) -> np.ndarray:
        """将输入转换为numpy数组"""
        if isinstance(image, Image.Image):
            image_np = np.array(image)
        else:
            image_np = image.copy()
        
        # 确保数据类型正确
        if image_np.dtype != np.uint8:
            if is_mask:
                # 对于mask，二值化
                image_np = (image_np > 0.5 * 255).astype(np.uint8) * 255
            else:
                # 对于图像，归一化到0-255
                if image_np.max() <= 1.0:
                    image_np = (image_np * 255).astype(np.uint8)
                else:
                    image_np = image_np.astype(np.uint8)
        
        return image_np
    
    def create_mask_from_regions(self, image_shape: Tuple[int, int], regions: list) -> np.ndarray:
        """
        从选定区域创建补全遮罩
        
        Args:
            image_shape: 图像尺寸 (height, width)
            regions: 选定的区域列表
            
        Returns:
            补全遮罩
        """
        mask = np.zeros(image_shape[:2], dtype=np.uint8)
        
        for region in regions:
            if isinstance(region, dict) and 'segmentation' in region:
                region_mask = region['segmentation']
                if isinstance(region_mask, np.ndarray):
                    mask = np.logical_or(mask, region_mask).astype(np.uint8) * 255
        
        return mask


# 便捷函数
def create_lama_inpainter(model_path: str = "models/lama/big-lama") -> LAMAInpainter:
    """创建LAMA补全器实例"""
    return LAMAInpainter(model_path)


def simple_inpaint(image: Union[np.ndarray, Image.Image], 
                  mask: Union[np.ndarray, Image.Image],
                  method: str = "telea") -> Image.Image:
    """
    简单的图像补全函数
    
    Args:
        image: 输入图像
        mask: 补全遮罩
        method: 补全方法
        
    Returns:
        补全后的图像
    """
    inpainter = LAMAInpainter()
    return inpainter.inpaint(image, mask, method)