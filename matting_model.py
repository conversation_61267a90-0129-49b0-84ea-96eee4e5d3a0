"""
MODNet Matting Model Integration - Enhanced with Real AI Model
用于高质量抠图的 Matting 模型 - 集成真正的AI模型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from PIL import Image
import cv2
import os
import requests
from typing import Tuple, Optional

# Try to import the full MODNet architecture
try:
    from modnet_arch_full import MODNet, load_modnet_model, MODNET_AVAILABLE
    if not MODNET_AVAILABLE:
        print("⚠️ Official MODNet architecture not available, using guided filter fallback")
except ImportError:
    MODNET_AVAILABLE = False
    print("⚠️ MODNet architecture module not available, using guided filter fallback")


class MODNetWrapper:
    """MODNet 模型封装类 - 支持真正的AI模型和CPU fallback"""
    
    def __init__(self, ckpt_path: Optional[str] = None, use_gpu: bool = True):
        """
        初始化 MODNet 模型
        
        Args:
            ckpt_path: 模型权重文件路径
            use_gpu: 是否尝试使用GPU
        """
        self.device = self._get_device(use_gpu)
        self.model = None
        self.use_real_model = False
        self.model_type = "guided_filter"  # Default fallback
        
        # Try to find checkpoint
        if ckpt_path is None:
            ckpt_path = self._find_checkpoint()
        
        self.ckpt_path = ckpt_path
        
        # Try to load real AI model
        self._download_model_if_needed()
        self._load_model()
    
    def _get_device(self, use_gpu: bool = True):
        """Get the appropriate device (GPU/CPU)"""
        if use_gpu and torch.cuda.is_available():
            device = torch.device('cuda')
            try:
                # Test GPU functionality
                test_tensor = torch.randn(10, 10).to(device)
                _ = torch.mm(test_tensor, test_tensor.t())
                
                gpu_name = torch.cuda.get_device_name(0)
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
                print(f"🚀 Using GPU: {gpu_name} ({gpu_memory:.1f}GB)")
                return device
            except Exception as e:
                print(f"⚠️ GPU test failed: {e}")
                print("🔄 Falling back to CPU")
                return torch.device('cpu')
        else:
            print("💻 Using CPU")
            return torch.device('cpu')
    
    def _find_checkpoint(self):
        """Find available MODNet checkpoint"""
        possible_paths = [
            "checkpoints/modnet_photographic_portrait_matting.pth",  # 优先查找.pth文件
            "checkpoints/modnet_webcam_portrait_matting.pth",
            "checkpoints/modnet_photographic_portrait_matting.ckpt",
            "checkpoints/modnet_webcam_portrait_matting.ckpt",
            "models/modnet_photographic_portrait_matting.pth",
            "models/modnet_webcam_portrait_matting.pth",
            "models/modnet_photographic_portrait_matting.ckpt",
            "models/modnet_webcam_portrait_matting.ckpt"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                file_size = os.path.getsize(path)
                if file_size > 10_000_000:  # > 10MB
                    print(f"🔍 Found MODNet checkpoint: {path} ({file_size / 1024 / 1024:.1f}MB)")
                    return path
        
        return None
    
    def _download_model_if_needed(self):
        """下载模型（如果需要且可能）"""
        if self.ckpt_path and os.path.exists(self.ckpt_path):
            return True
            
        if not MODNET_AVAILABLE:
            print("📦 MODNet architecture not available, skipping download")
            return False
        
        # Try to download if not exists
        checkpoint_dir = "checkpoints"
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        # 使用clibdev/MODNet的稳定版本
        model_url = "https://github.com/clibdev/MODNet/releases/latest/download/modnet-photographic.pt"
        checkpoint_path = os.path.join(checkpoint_dir, "modnet_photographic_portrait_matting.pth")
        
        if os.path.exists(checkpoint_path):
            self.ckpt_path = checkpoint_path
            return True
        
        try:
            print(f"📥 Downloading MODNet checkpoint from {model_url}")
            import requests
            response = requests.get(model_url)
            response.raise_for_status()
            
            with open(checkpoint_path, 'wb') as f:
                f.write(response.content)
            
            print("✅ MODNet checkpoint downloaded successfully!")
            self.ckpt_path = checkpoint_path
            return True
            
        except Exception as e:
            print(f"❌ Failed to download checkpoint: {e}")
            print("💬 You can:")
            print(f"1. Manually download from: {model_url}")
            print(f"2. Place it at: {checkpoint_path}")
            return False
    
    def _load_model(self):
        """加载 MODNet 模型"""
        # Try to load real AI model
        if MODNET_AVAILABLE and self.ckpt_path and os.path.exists(self.ckpt_path):
            try:
                print(f"🤖 Loading MODNet AI model from: {self.ckpt_path}")
                # 支持.pth和.ckpt后缀
                self.model = load_modnet_model(self.ckpt_path, self.device)
                self.use_real_model = True
                self.model_type = "modnet_ai"
                
                # Test the model
                test_input = torch.randn(1, 3, 512, 512).to(self.device)
                with torch.no_grad():
                    output = self.model(test_input, inference=True)
                    if output.shape == (1, 1, 512, 512):
                        print("✅ MODNet AI model loaded and tested successfully!")
                        return True
                    else:
                        raise ValueError(f"Unexpected output shape: {output.shape}")
                        
            except Exception as e:
                print(f"❌ Failed to load MODNet AI model: {e}")
                print("🔄 Falling back to guided filter method")
                self.use_real_model = False
                self.model = None
                self.model_type = "guided_filter"
        
        # Fallback to guided filter
        if not self.use_real_model:
            print("🎨 Using enhanced guided filter matting method (no AI model download needed)")
            self.model_type = "guided_filter"
        
        return True

    def create_trimap_from_mask(self, mask: np.ndarray, dilate_size: int = 10) -> np.ndarray:
        """
        从二值 mask 创建 trimap
        
        Args:
            mask: 二值 mask (0 或 255)
            dilate_size: 膨胀大小，用于创建不确定区域
            
        Returns:
            trimap: 0=背景, 128=不确定, 255=前景
        """
        if mask.max() <= 1:
            mask = (mask * 255).astype(np.uint8)
        else:
            mask = mask.astype(np.uint8)
        
        # 创建形态学操作的核
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (dilate_size, dilate_size))
        
        # 膨胀和腐蚀操作
        dilated = cv2.dilate(mask, kernel, iterations=1)
        eroded = cv2.erode(mask, kernel, iterations=1)
        
        # 创建 trimap
        trimap = np.zeros_like(mask)
        trimap[eroded > 0] = 255  # 确定前景
        trimap[dilated == 0] = 0   # 确定背景
        trimap[(dilated > 0) & (eroded == 0)] = 128  # 不确定区域
        
        return trimap

    def preprocess_image(self, image: np.ndarray, ref_size: int = 512) -> Tuple[torch.Tensor, Tuple[int, int]]:
        """
        预处理图像用于 MODNet
        
        Args:
            image: 输入图像 (H, W, 3)
            ref_size: 参考尺寸
            
        Returns:
            预处理后的张量和原始尺寸
        """
        h, w = image.shape[:2]
        
        # 计算新的尺寸，保持长宽比
        if h >= w:
            new_h = ref_size
            new_w = int(w * ref_size / h)
        else:
            new_h = int(h * ref_size / w)
            new_w = ref_size
        
        # 确保尺寸是32的倍数（对于某些模型架构）
        new_h = ((new_h + 31) // 32) * 32
        new_w = ((new_w + 31) // 32) * 32
        
        # 调整大小
        image_resized = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)
        
        # 转换为张量
        image_tensor = torch.from_numpy(image_resized).float() / 255.0
        image_tensor = image_tensor.permute(2, 0, 1).unsqueeze(0)  # (1, 3, H, W)
        
        # 标准化 (ImageNet stats)
        mean = torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1)
        std = torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1)
        image_tensor = (image_tensor - mean) / std
        
        return image_tensor.to(self.device), (h, w)

    def predict(self, image: np.ndarray, mask: np.ndarray, 
                trimap_size: int = 10, quality: str = "balanced") -> np.ndarray:
        """
        使用 MODNet 进行抠图
        
        Args:
            image: 输入图像 (H, W, 3)
            mask: SAM 生成的二值 mask
            trimap_size: trimap 不确定区域大小
            quality: 质量设置 ("fast", "balanced", "high")
            
        Returns:
            alpha matte (H, W) 范围 [0, 1]
        """
        
        if self.use_real_model and self.model is not None:
            return self._predict_with_ai_model(image, mask, quality)
        else:
            return self._predict_with_guided_filter(image, mask, trimap_size)
    
    def _predict_with_ai_model(self, image: np.ndarray, mask: np.ndarray, quality: str) -> np.ndarray:
        """使用真正的AI模型进行预测"""
        try:
            with torch.no_grad():
                # 预处理图像
                ref_size = {"fast": 384, "balanced": 512, "high": 768}.get(quality, 512)
                image_tensor, original_size = self.preprocess_image(image, ref_size)
                
                # AI模型推理
                pred_matte = self.model(image_tensor, inference=True)
                
                # 后处理
                pred_matte = pred_matte.squeeze().cpu().numpy()
                
                # 调整回原始尺寸
                if pred_matte.shape != original_size:
                    pred_matte = cv2.resize(pred_matte, (original_size[1], original_size[0]), 
                                          interpolation=cv2.INTER_LINEAR)
                
                # 使用SAM mask作为引导，改进AI预测结果
                if mask.max() > 1:
                    sam_mask = mask.astype(np.float32) / 255.0
                else:
                    sam_mask = mask.astype(np.float32)
                
                # 融合SAM mask和AI预测
                # 在SAM确定的前景区域，增强AI预测的置信度
                enhanced_matte = pred_matte.copy()
                fg_regions = sam_mask > 0.8
                enhanced_matte[fg_regions] = np.maximum(enhanced_matte[fg_regions], sam_mask[fg_regions])
                
                # 在SAM确定的背景区域，降低AI预测
                bg_regions = sam_mask < 0.2
                enhanced_matte[bg_regions] = np.minimum(enhanced_matte[bg_regions], sam_mask[bg_regions])
                
                return enhanced_matte
                
        except Exception as e:
            print(f"⚠️ AI model prediction failed: {e}")
            print("🔄 Falling back to guided filter method")
            return self._predict_with_guided_filter(image, mask, 10)
    
    def _predict_with_guided_filter(self, image: np.ndarray, mask: np.ndarray, trimap_size: int) -> np.ndarray:
        """使用引导滤波进行预测（CPU fallback）"""
        # 创建 trimap
        trimap = self.create_trimap_from_mask(mask, trimap_size)
        
        # 使用改进的引导滤波
        matte = self._guided_filter_matting(image, mask, trimap)
        
        return matte

    def _guided_filter_matting(self, image: np.ndarray, mask: np.ndarray, 
                              trimap: np.ndarray) -> np.ndarray:
        """
        使用改进的引导滤波进行 matting
        """
        # 将 mask 转换为浮点数
        if mask.max() > 1:
            mask = mask.astype(np.float32) / 255.0
        else:
            mask = mask.astype(np.float32)
        
        # 获取不确定区域
        uncertain_mask = (trimap == 128)
        
        # 多尺度引导滤波
        guide = image.astype(np.float32) / 255.0
        
        # 使用多个尺度的引导滤波
        scales = [5, 10, 20, 40]
        eps_values = [1e-5, 1e-4, 1e-3, 1e-2]
        
        refined_alphas = []
        weights = [0.4, 0.3, 0.2, 0.1]  # 不同尺度的权重
        
        for i, (radius, eps) in enumerate(zip(scales, eps_values)):
            # 改进的引导滤波实现
            refined = self._advanced_guided_filter(guide, mask, radius, eps)
            refined_alphas.append(refined * weights[i])
        
        # 加权融合多尺度结果
        final_alpha = np.sum(refined_alphas, axis=0)
        
        # 边缘增强处理
        final_alpha = self._enhance_edges(final_alpha, image)
        
        # 只在不确定区域应用细化结果，保留SAM的确定区域
        result = mask.copy()
        if np.any(uncertain_mask):
            result[uncertain_mask] = final_alpha[uncertain_mask]
        
        # 后处理：平滑和约束
        result = self._post_process_matte(result, mask)
        
        # 确保值在 [0, 1] 范围内
        result = np.clip(result, 0, 1)
        
        return result

    def _advanced_guided_filter(self, guide: np.ndarray, src: np.ndarray, 
                               radius: int, eps: float) -> np.ndarray:
        """改进的引导滤波实现"""
        # 如果是彩色图像，使用所有通道
        if len(guide.shape) == 3:
            # 对每个通道分别处理，然后融合
            results = []
            for c in range(guide.shape[2]):
                guide_c = guide[:, :, c]
                result_c = self._single_channel_guided_filter(guide_c, src, radius, eps)
                results.append(result_c)
            
            # 加权融合（绿色通道权重更大）
            weights = [0.299, 0.587, 0.114] if len(results) == 3 else [1.0/len(results)] * len(results)
            final_result = np.zeros_like(src)
            for i, (result, weight) in enumerate(zip(results, weights)):
                final_result += result * weight
            
            return final_result
        else:
            return self._single_channel_guided_filter(guide, src, radius, eps)
    
    def _single_channel_guided_filter(self, guide: np.ndarray, src: np.ndarray, 
                                    radius: int, eps: float) -> np.ndarray:
        """单通道引导滤波"""
        # 均值滤波
        mean_I = cv2.blur(guide, (radius, radius))
        mean_p = cv2.blur(src, (radius, radius))
        corr_Ip = cv2.blur(guide * src, (radius, radius))
        
        # 协方差
        cov_Ip = corr_Ip - mean_I * mean_p
        
        # 方差
        mean_II = cv2.blur(guide * guide, (radius, radius))
        var_I = mean_II - mean_I * mean_I
        
        # 线性系数
        a = cov_Ip / (var_I + eps)
        b = mean_p - a * mean_I
        
        # 平滑系数
        mean_a = cv2.blur(a, (radius, radius))
        mean_b = cv2.blur(b, (radius, radius))
        
        # 最终结果
        return mean_a * guide + mean_b

    def _enhance_edges(self, alpha: np.ndarray, image: np.ndarray) -> np.ndarray:
        """边缘增强处理"""
        # 计算梯度
        grad_x = cv2.Sobel(alpha, cv2.CV_32F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(alpha, cv2.CV_32F, 0, 1, ksize=3)
        grad_mag = np.sqrt(grad_x**2 + grad_y**2)
        
        # 图像的边缘信息
        gray = cv2.cvtColor((image * 255).astype(np.uint8), cv2.COLOR_RGB2GRAY)
        img_grad_x = cv2.Sobel(gray, cv2.CV_32F, 1, 0, ksize=3)
        img_grad_y = cv2.Sobel(gray, cv2.CV_32F, 0, 1, ksize=3)
        img_grad_mag = np.sqrt(img_grad_x**2 + img_grad_y**2) / 255.0
        
        # 在边缘区域应用增强
        edge_threshold = 0.1
        edge_mask = (grad_mag > edge_threshold) | (img_grad_mag > edge_threshold)
        
        enhanced = alpha.copy()
        if np.any(edge_mask):
            # 使用sigmoid函数增强对比度
            enhanced[edge_mask] = self._sigmoid_enhance(enhanced[edge_mask], factor=2.0)
        
        return enhanced

    def _sigmoid_enhance(self, x: np.ndarray, factor: float = 1.5) -> np.ndarray:
        """使用sigmoid函数增强对比度"""
        # 将 [0,1] 映射到 [-6,6] 然后应用sigmoid
        x_scaled = (x - 0.5) * 12 * factor
        return 1.0 / (1.0 + np.exp(-x_scaled))
    
    def _post_process_matte(self, matte: np.ndarray, original_mask: np.ndarray) -> np.ndarray:
        """后处理matte"""
        # 轻微的高斯模糊来平滑
        smoothed = cv2.GaussianBlur(matte, (3, 3), 0.5)
        
        # 在确定区域保持原始mask的约束
        if original_mask.max() > 1:
            orig_norm = original_mask.astype(np.float32) / 255.0
        else:
            orig_norm = original_mask.astype(np.float32)
        
        # 强前景区域
        strong_fg = orig_norm > 0.9
        smoothed[strong_fg] = np.maximum(smoothed[strong_fg], 0.9)
        
        # 强背景区域
        strong_bg = orig_norm < 0.1
        smoothed[strong_bg] = np.minimum(smoothed[strong_bg], 0.1)
        
        return smoothed

    def enhance_details(self, matte: np.ndarray, factor: float = 1.0) -> np.ndarray:
        """
        增强 matte 的细节
        
        Args:
            matte: Alpha matte
            factor: 增强因子
            
        Returns:
            增强后的 matte
        """
        if factor == 1.0:
            return matte
        
        # 使用 Unsharp Masking 增强细节
        blurred = cv2.GaussianBlur(matte, (5, 5), 1.0)
        enhanced = matte + factor * (matte - blurred)
        
        # 约束到有效范围
        enhanced = np.clip(enhanced, 0, 1)
        
        return enhanced

    def get_model_info(self) -> dict:
        """获取模型信息"""
        return {
            "model_type": self.model_type,
            "use_real_model": self.use_real_model,
            "device": str(self.device),
            "checkpoint_path": self.ckpt_path,
            "modnet_available": MODNET_AVAILABLE
        }


def create_rgba_with_matte(image: np.ndarray, matte: np.ndarray) -> Image.Image:
    """
    使用 matte 创建 RGBA 图像
    
    Args:
        image: 原始图像 (H, W, 3)
        matte: Alpha matte (H, W) 范围 [0, 1]
        
    Returns:
        RGBA PIL 图像
    """
    # 确保输入格式正确
    if image.dtype != np.uint8:
        if image.max() <= 1:
            image = (image * 255).astype(np.uint8)
        else:
            image = image.astype(np.uint8)
    
    if matte.dtype != np.uint8:
        if matte.max() <= 1:
            matte = (matte * 255).astype(np.uint8)
        else:
            matte = matte.astype(np.uint8)
    
    # 确保尺寸匹配
    if image.shape[:2] != matte.shape:
        matte = cv2.resize(matte, (image.shape[1], image.shape[0]), interpolation=cv2.INTER_LINEAR)
    
    # 创建 RGBA 图像
    rgba = np.zeros((image.shape[0], image.shape[1], 4), dtype=np.uint8)
    rgba[:, :, :3] = image
    rgba[:, :, 3] = matte
    
    return Image.fromarray(rgba, 'RGBA')


# 简单的测试函数
def test_matting():
    """测试 matting 功能"""
    print("测试 Matting 功能...")
    
    # 创建测试图像和 mask
    test_image = np.ones((256, 256, 3), dtype=np.uint8) * 255
    test_mask = np.zeros((256, 256), dtype=np.uint8)
    cv2.circle(test_mask, (128, 128), 80, 255, -1)
    
    # 初始化 matting 模型
    matting = MODNetWrapper()
    
    # 进行 matting
    matte = matting.predict(test_image, test_mask)
    
    print(f"Matte shape: {matte.shape}, range: [{matte.min():.2f}, {matte.max():.2f}]")
    
    # 创建 RGBA 图像
    result = create_rgba_with_matte(test_image, matte)
    
    print("Matting 测试完成！")
    return result


if __name__ == "__main__":
    test_matting() 