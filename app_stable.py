import gradio as gr
import numpy as np
from PIL import Image
import traceback
import os
import cv2
import time

# Import our custom modules
from sam_model import SAMPredictor
from matting_model import MODNetWrapper, create_rgba_with_matte
from utils import (
    pil_to_numpy, numpy_to_pil, visualize_masks, visualize_masks_with_borders,
    find_mask_at_point, resize_image_for_display, merge_masks,
    apply_mask_to_image, create_cutout_image
)

class AIImageCutout:
    def __init__(self, model_size: str = "tiny"):
        """
        Initialize the AI Image Cutout application
        """
        self.model_size = model_size
        self.predictor = None
        self.matting_model = None
        self.current_image = None  # 原始图像
        self.display_image = None  # 调整后的显示图像
        self.current_masks = []
        self.selected_masks = []  # 存储选中的区域
        self.segmentation_image = None
        self.last_cutout_file = None
        self.selection_mode = "single"  # "single" or "multiple"
        
        # Matting 参数
        self.use_matting = True
        self.trimap_size = 10
        self.matting_quality = "balanced"
        self.detail_enhancement = 1.0
        
        # Initialize models
        self._init_models()
    
    def _init_models(self):
        """Initialize models"""
        try:
            # Initialize SAM model
            print(f"Loading SAM2 {self.model_size} model...")
            self.predictor = SAMPredictor(model_size=self.model_size)
            print("SAM model loaded successfully!")
            
            # Initialize Matting model
            print("Loading Matting model...")
            # 指定官方MODNet权重路径
            modnet_path = "checkpoints/modnet_photographic_portrait_matting.pth"
            self.matting_model = MODNetWrapper(ckpt_path=modnet_path)
            
            # 获取模型状态
            model_info = self.matting_model.get_model_info()
            if model_info["use_real_model"]:
                print(f"✅ MODNet AI model loaded successfully! ({model_info['model_type']})")
            else:
                print(f"⚠️ Using fallback matting method: {model_info['model_type']}")
            
            return True
            
        except Exception as e:
            print(f"Model initialization error: {e}")
            traceback.print_exc()
            return False
    
    def upload_image(self, image: Image.Image) -> tuple:
        """Handle image upload and run SAM prediction"""
        if image is None:
            return None, "请上传图片"
        
        try:
            # Convert and store image
            self.current_image = pil_to_numpy(image)
            print(f"Image set successfully. Shape: {self.current_image.shape}")
            
            # Resize for display if needed
            self.display_image = resize_image_for_display(self.current_image)
            
            # Clear previous results
            self.current_masks = []
            self.selected_masks = []
            
            # Run SAM prediction
            print("Generating masks...")
            if self.predictor and self.display_image is not None:
                # Set image for prediction
                self.predictor.set_image(self.display_image)
                
                # Generate masks using predict_everything
                self.current_masks = self.predictor.predict_everything()
                print(f"Generated {len(self.current_masks)} masks")
                
                # Visualize masks - 使用display_image确保尺寸一致
                if self.current_masks:
                    self.segmentation_image = visualize_masks_with_borders(
                        self.display_image, self.current_masks, []
                    )
                    return numpy_to_pil(self.segmentation_image), f"分析完成！检测到 {len(self.current_masks)} 个可选区域，点击选择"
                else:
                    return image, "未检测到明显区域，请尝试其他图片"
            else:
                return image, "模型未正确加载"
                
        except Exception as e:
            error_msg = f"图片处理失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return image, error_msg
    
    def on_image_click(self, evt: gr.SelectData) -> tuple:
        """Handle click on the segmentation image"""
        if self.current_masks is None or len(self.current_masks) == 0:
            return None, None, "请先上传并分析图片", None
        
        try:
            # 确保evt不为None且有index属性
            if evt is None or not hasattr(evt, 'index'):
                return None, None, "无效的点击事件", None
                
            # 获取点击坐标
            x, y = evt.index[0], evt.index[1]
            print(f"Click at: ({x}, {y})")
            
            # Find mask at clicked point
            clicked_mask = find_mask_at_point(self.current_masks, (x, y))
            
            if clicked_mask is None:
                return None, None, "未找到可选区域，请点击其他位置", None
            
            if self.selection_mode == "single":
                # Single selection mode
                self.selected_masks = [clicked_mask]
                
                # Update visualization
                self.segmentation_image = visualize_masks_with_borders(
                    self.display_image, self.current_masks, self.selected_masks
                )
                
                # Generate cutout
                return self._generate_cutout_result()
            
            else:
                # Multiple selection mode
                # 检查是否已经选择 - 使用ID或其他唯一标识符比较
                mask_already_selected = False
                for i, selected_mask in enumerate(self.selected_masks):
                    if np.array_equal(selected_mask['segmentation'], clicked_mask['segmentation']):
                        # Remove if already selected
                        self.selected_masks.pop(i)
                        mask_already_selected = True
                        break
                
                if not mask_already_selected:
                    # Add to selection
                    self.selected_masks.append(clicked_mask)
                
                # Update visualization
                self.segmentation_image = visualize_masks_with_borders(
                    self.display_image, self.current_masks, self.selected_masks
                )
                
                status = f"已选择 {len(self.selected_masks)} 个区域，多选模式请点击'处理选中区域'按钮生成结果"
                return numpy_to_pil(self.segmentation_image), None, status, None
                
        except Exception as e:
            error_msg = f"点击处理失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return None, None, error_msg, None
    
    def _generate_cutout_result(self) -> tuple:
        """Generate cutout result for selected masks"""
        try:
            if not self.selected_masks:
                return None, None, "请先选择要抠出的区域", None
            
            # Merge selected masks
            merged_mask = merge_masks([mask['segmentation'] for mask in self.selected_masks])
            
            # Resize mask to match original image if needed
            original_h, original_w = self.current_image.shape[:2]
            display_h, display_w = self.display_image.shape[:2]
            
            if original_h != display_h or original_w != display_w:
                merged_mask = cv2.resize(
                    merged_mask.astype(np.uint8), 
                    (original_w, original_h), 
                    interpolation=cv2.INTER_NEAREST
                ).astype(bool)
            
            # Create cutout image
            if self.use_matting and self.matting_model:
                # Use matting for better edges
                print("Using Matting for high-quality edges...")
                matte = self.matting_model.predict(
                    self.current_image,
                    merged_mask,
                    trimap_size=self.trimap_size,
                    quality=self.matting_quality
                )
                
                # Apply detail enhancement
                if self.detail_enhancement != 1.0:
                    matte = self.matting_model.enhance_details(matte, self.detail_enhancement)
                
                # Create RGBA image with matte
                cutout_image = create_rgba_with_matte(self.current_image, matte)
            else:
                # Simple mask application
                print("Using simple mask application...")
                cutout_image = create_cutout_image(
                    self.current_image, 
                    merged_mask,
                    edge_method="smooth",
                    smooth_strength=0.5,
                    feather_radius=2
                )
            
            # Create output file
            timestamp = int(time.time())
            output_filename = f"cutout_{timestamp}.png"
            output_path = os.path.join("outputs", output_filename)
            
            # Ensure output directory exists
            os.makedirs("outputs", exist_ok=True)
            
            # Save cutout result
            cutout_image.save(output_path, 'PNG')
            self.last_cutout_file = output_path
            
            status_msg = f"抠图完成！"
            if self.use_matting:
                status_msg += f" | 使用 Matting 增强"
            
            return (
                numpy_to_pil(self.segmentation_image),
                cutout_image,
                status_msg,
                output_path
            )
            
        except Exception as e:
            error_msg = f"抠图生成失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return None, None, error_msg, None
    
    def export_selected(self) -> tuple:
        """Export selected regions"""
        return self._generate_cutout_result()
    
    def change_sam_model(self, new_model_size: str, current_image: Image.Image) -> tuple:
        """Change SAM model size and re-analyze current image"""
        try:
            if new_model_size == self.model_size:
                return None, f"已经是 {new_model_size} 模型"
            
            print(f"Switching to SAM2 {new_model_size} model...")
            self.model_size = new_model_size
            
            # Reload SAM model
            self.predictor = SAMPredictor(model_size=new_model_size)
            print(f"已切换到 {new_model_size} 模型")
            
            # Re-analyze current image if available
            if current_image is not None:
                return self.upload_image(current_image)
            else:
                return None, f"已切换到 {new_model_size} 模型，请重新上传图片"
                
        except Exception as e:
            error_msg = f"模型切换失败: {str(e)}"
            print(error_msg)
            return None, error_msg
    
    def clear_selection(self) -> tuple:
        """Clear current selection"""
        self.selected_masks = []
        
        # Update visualization
        if self.segmentation_image is not None and self.display_image is not None:
            self.segmentation_image = visualize_masks_with_borders(
                self.display_image, self.current_masks, self.selected_masks
            )
            return numpy_to_pil(self.segmentation_image), None, "已清空所有选择"
        
        return None, None, "已清空所有选择"
    
    def update_matting_params(self, use_matting: bool, trimap_size: int, 
                            matting_quality: str, detail_enhancement: float) -> tuple:
        """更新Matting参数"""
        # 更新参数
        self.use_matting = use_matting
        self.trimap_size = trimap_size
        self.matting_quality = matting_quality
        self.detail_enhancement = detail_enhancement
        
        # 如果有选中的mask，重新生成抠图
        if self.selected_masks:
            return self._generate_cutout_result()
        
        return None, None, "参数已更新", None
    
    def update_selection_mode(self, mode: str) -> tuple:
        """更新选择模式"""
        self.selection_mode = mode
        
        # 清空当前选择
        self.selected_masks = []
        
        # 更新可视化
        if self.segmentation_image is not None and self.display_image is not None:
            self.segmentation_image = visualize_masks_with_borders(
                self.display_image, self.current_masks, self.selected_masks
            )
            return numpy_to_pil(self.segmentation_image), None, f"已切换到{mode}模式"
        
        return None, None, f"已切换到{mode}模式"


def create_interface():
    """Create the Gradio interface"""
    
    # Initialize the application
    app = AIImageCutout(model_size="tiny")
    
    with gr.Blocks(title="AI智能抠图工具", theme=gr.themes.Soft()) as demo:
        gr.Markdown("""
        # 🎨 AI智能抠图工具
        
        使用 SAM2 + Matting 技术实现高质量抠图
        """)
        
        # 运行状态显示
        device = app.predictor.device if hasattr(app.predictor, 'device') else 'cpu'
        matting_info = "引导滤波" if not hasattr(app.matting_model, 'model') else "MODNet AI"
        status_box = gr.Textbox(
            label="运行状态",
            value=f"SAM模型: {app.model_size}\n运行设备: {device}\nMatting方法: {matting_info}",
            interactive=False,
            lines=3
        )
        
        with gr.Row():
            with gr.Column(scale=1):
                # 图片上传
                input_image = gr.Image(
                    label="上传图片",
                    type="pil",
                    elem_id="input_image"
                )
                
                # 核心设置
                with gr.Group():
                    gr.Markdown("### 核心设置")
                    
                    # SAM 模型选择
                    model_selector = gr.Dropdown(
                        choices=["tiny", "small", "base_plus", "large"],
                        value="tiny",
                        label="SAM2 模型",
                        info="tiny(最快) → small → base_plus → large(最准确)"
                    )
                    
                    # 选择模式
                    selection_mode = gr.Radio(
                        choices=["single", "multiple"],
                        value="single",
                        label="选择模式",
                        info="single：点击直接处理 | multiple：选择多个区域后统一处理"
                    )
                
                # Matting 设置
                with gr.Accordion("Matting 高级设置", open=False):
                    use_matting = gr.Checkbox(
                        value=True,
                        label="启用 Matting 增强",
                        info="使用AI算法优化边缘细节，特别适用于头发丝、半透明区域"
                    )
                    
                    with gr.Row():
                        trimap_size = gr.Slider(
                            minimum=5,
                            maximum=30,
                            step=5,
                            value=10,
                            label="边缘过渡区域",
                            info="控制边缘处理的精细程度"
                        )
                        
                        matting_quality = gr.Radio(
                            choices=["fast", "balanced", "high"],
                            value="balanced",
                            label="处理质量",
                            info="质量越高处理越慢"
                        )
                    
                    detail_enhancement = gr.Slider(
                        minimum=0.5,
                        maximum=2.0,
                        step=0.1,
                        value=1.0,
                        label="细节增强",
                        info="增强头发丝等细节的清晰度"
                    )
                    
                    update_params_btn = gr.Button("更新参数", variant="secondary")
                
                # 多选模式按钮
                with gr.Row(visible=False) as multi_select_buttons:
                    clear_btn = gr.Button("清空选择", size="sm")
                    export_btn = gr.Button("处理选中区域", variant="primary", size="sm")
                
                # 状态信息
                info_text = gr.Textbox(
                    label="提示信息",
                    interactive=False,
                    value="请上传图片开始使用",
                    lines=2
                )
            
            with gr.Column(scale=1):
                # 分割结果展示
                segmentation_output = gr.Image(
                    label="点击选择区域",
                    type="pil",
                    interactive=True,
                    elem_id="segmentation_output"
                )
                
                # 处理结果
                result_output = gr.Image(
                    label="抠图结果",
                    type="pil",
                    elem_id="result_output"
                )
                
                # 下载按钮
                download_btn = gr.File(
                    label="下载文件",
                    visible=True,
                    elem_id="download_btn"
                )
        
        # 事件处理
        def update_status():
            device = app.predictor.device if hasattr(app.predictor, 'device') else 'cpu'
            matting_info = "引导滤波" if not hasattr(app.matting_model, 'model') else "MODNet AI"
            return f"SAM模型: {app.model_size}\n运行设备: {device}\nMatting方法: {matting_info}"
        
        # 上传图片
        input_image.upload(
            fn=app.upload_image,
            inputs=[input_image],
            outputs=[segmentation_output, info_text]
        ).then(
            fn=update_status,
            outputs=[status_box]
        )
        
        # 图片变化时也触发分析
        input_image.change(
            fn=app.upload_image,
            inputs=[input_image],
            outputs=[segmentation_output, info_text]
        ).then(
            fn=update_status,
            outputs=[status_box]
        )
        
        # SAM 模型切换
        def model_change_handler(new_model, current_image):
            result = app.change_sam_model(new_model, current_image)
            status = update_status()
            return [*result, status]
            
        model_selector.change(
            fn=model_change_handler,
            inputs=[model_selector, input_image],
            outputs=[segmentation_output, info_text, status_box]
        )
        
        # 选择模式切换
        selection_mode.change(
            fn=app.update_selection_mode,
            inputs=[selection_mode],
            outputs=[segmentation_output, result_output, info_text]
        ).then(
            fn=update_status,
            outputs=[status_box]
        )
        
        # 点击分割图像
        segmentation_output.select(
            fn=app.on_image_click,
            inputs=None,
            outputs=[segmentation_output, result_output, info_text, download_btn]
        ).then(
            fn=update_status,
            outputs=[status_box]
        )
        
        # 清空选择
        clear_btn.click(
            fn=app.clear_selection,
            inputs=[],
            outputs=[segmentation_output, result_output, info_text]
        ).then(
            fn=update_status,
            outputs=[status_box]
        )
        
        # 导出选中区域
        def export_handler():
            try:
                result = app.export_selected()
                if len(result) == 4:
                    return result[1], result[2], result[3], update_status()
                else:
                    return None, "处理失败", None, update_status()
            except Exception as e:
                print(f"导出错误: {e}")
                return None, f"导出失败: {str(e)}", None, update_status()
        
        export_btn.click(
            fn=export_handler,
            inputs=[],
            outputs=[result_output, info_text, download_btn, status_box]
        )
        
        # 更新参数
        def params_update_handler(*args):
            result = app.update_matting_params(*args)
            status = update_status()
            return [*result, status]
            
        update_params_btn.click(
            fn=params_update_handler,
            inputs=[use_matting, trimap_size, matting_quality, detail_enhancement],
            outputs=[result_output, info_text, download_btn, status_box]
        )
        
        # 根据选择模式显示/隐藏按钮
        def update_ui_visibility(mode):
            return gr.update(visible=(mode == "multiple"))
        
        selection_mode.change(
            fn=update_ui_visibility,
            inputs=[selection_mode],
            outputs=[multi_select_buttons]
        )
    
    return demo


def main():
    """Main function to run the application"""
    print("=" * 60)
    print("🚀 启动 AI智能抠图工具")
    print("=" * 60)
    
    # Create and launch the interface
    demo = create_interface()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7862,  # 使用7862端口
        share=False,
        show_error=True,
        quiet=False
    )


if __name__ == "__main__":
    main() 